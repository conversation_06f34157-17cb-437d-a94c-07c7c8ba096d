<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>2.1.1.RELEASE</version>
	</parent>

	<groupId>com.coohua.caf</groupId>
	<artifactId>caf-boot-parent</artifactId>
	<version>2.0.12-SNAPSHOT</version>
	<packaging>pom</packaging>

	<properties>
		<spring.boot.version>2.1.1.RELEASE</spring.boot.version>
		<fastjson.version>1.2.58</fastjson.version>
		<lmax.disruptor.version>3.4.2</lmax.disruptor.version>
		<conversantmedia.disruptor.version>1.2.11</conversantmedia.disruptor.version>
		<mysql-connector-java.version>5.1.38</mysql-connector-java.version>
		<mybatis.version>3.4.5</mybatis.version>
		<mybatis-spring.version>1.3.2</mybatis-spring.version>
		<druid.version>1.1.19</druid.version>
		<com.weibo.motan>1.1.1</com.weibo.motan>
		<hystrix.version>1.5.9</hystrix.version>
		<apollo-client.version>1.0.0</apollo-client.version>
		<commons-collections4.version>4.2</commons-collections4.version>
		<guava.version>18.0</guava.version>
		<joda-convert.version>2.1.1</joda-convert.version>
		<prometheus.client.version>0.5.0</prometheus.client.version>
		<rocketmq.client.version>4.3.2</rocketmq.client.version>
		<elasticsearch.client.version>6.5.4</elasticsearch.client.version>
		<aliyun.oss.client.version>2.6.0</aliyun.oss.client.version>
		<aliyun.java.sdk.cdn.version>3.0.8</aliyun.java.sdk.cdn.version>
		<aliyun.java.sdk.core.version>4.4.1</aliyun.java.sdk.core.version>
		<pepper.metrics.version>1.0.14</pepper.metrics.version>
	</properties>

	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>top.zrbcool</groupId>
				<artifactId>pepper-metrics-mybatis</artifactId>
				<version>${pepper.metrics.version}</version>
			</dependency>
			<dependency>
				<groupId>top.zrbcool</groupId>
				<artifactId>pepper-metrics-core</artifactId>
				<version>${pepper.metrics.version}</version>
			</dependency>
			<dependency>
				<groupId>top.zrbcool</groupId>
				<artifactId>pepper-metrics-scheduled-printer</artifactId>
				<version>${pepper.metrics.version}</version>
			</dependency>
			<dependency>
				<groupId>top.zrbcool</groupId>
				<artifactId>pepper-metrics-ds-prometheus</artifactId>
				<version>${pepper.metrics.version}</version>
			</dependency>
			<dependency>
				<groupId>top.zrbcool</groupId>
				<artifactId>pepper-metrics-servlet</artifactId>
				<version>${pepper.metrics.version}</version>
			</dependency>
            <dependency>
                <groupId>top.zrbcool</groupId>
                <artifactId>pepper-metrics-jedis</artifactId>
                <version>${pepper.metrics.version}</version>
            </dependency>
			<dependency>
				<groupId>top.zrbcool</groupId>
				<artifactId>pepper-metrics-druid</artifactId>
				<version>${pepper.metrics.version}</version>
			</dependency>
			<dependency>
				<groupId>top.zrbcool</groupId>
				<artifactId>pepper-metrics-motan</artifactId>
				<version>${pepper.metrics.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.httpcomponents</groupId>
				<artifactId>httpclient</artifactId>
				<version>4.5.8</version>
			</dependency>
			<dependency>
				<groupId>org.apache.httpcomponents</groupId>
				<artifactId>httpcore-nio</artifactId>
				<version>4.4.11</version>
			</dependency>
			<dependency>
				<groupId>org.apache.httpcomponents</groupId>
				<artifactId>httpasyncclient</artifactId>
				<version>4.1.4</version>
			</dependency>
			<dependency>
				<groupId>com.alibaba.csp</groupId>
				<artifactId>ahas-sentinel-client</artifactId>
				<version>1.2.1</version>
			</dependency>
			<dependency>
				<groupId>com.alibaba.csp</groupId>
				<artifactId>sentinel-web-servlet</artifactId>
				<version>1.5.2</version>
			</dependency>
			<dependency>
				<groupId>com.alibaba.csp</groupId>
				<artifactId>sentinel-annotation-aspectj</artifactId>
				<version>1.5.2</version>
			</dependency>
			<dependency>
				<groupId>com.alibaba.csp</groupId>
				<artifactId>sentinel-core</artifactId>
				<version>1.5.2</version>
			</dependency>
			<dependency>
				<groupId>com.alibaba.csp</groupId>
				<artifactId>sentinel-transport-simple-http</artifactId>
				<version>1.5.2</version>
			</dependency>
			<dependency>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-dependencies</artifactId>
				<version>${spring.boot.version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
			<dependency>
				<groupId>com.coohua.caf</groupId>
				<artifactId>caf-boot-starter</artifactId>
				<version>2.0.12-SNAPSHOT</version>
			</dependency>
			<dependency>
				<groupId>com.coohua.caf</groupId>
				<artifactId>caf-core</artifactId>
				<version>2.0.12-SNAPSHOT</version>
			</dependency>
			<dependency>
				<groupId>com.coohua.caf</groupId>
				<artifactId>caf-boot-starter-common</artifactId>
				<version>2.0.12-SNAPSHOT</version>
			</dependency>
			<dependency>
				<groupId>com.coohua.caf</groupId>
				<artifactId>caf-boot-starter-web</artifactId>
				<version>2.0.12-SNAPSHOT</version>
			</dependency>
			<dependency>
				<groupId>com.coohua.caf</groupId>
				<artifactId>caf-boot-starter-json</artifactId>
				<version>2.0.12-SNAPSHOT</version>
			</dependency>
			<dependency>
				<groupId>com.coohua.caf</groupId>
				<artifactId>caf-boot-starter-db</artifactId>
				<version>2.0.12-SNAPSHOT</version>
			</dependency>
			<dependency>
				<groupId>com.coohua.caf</groupId>
				<artifactId>caf-boot-starter-kv</artifactId>
				<version>2.0.12-SNAPSHOT</version>
			</dependency>
			<dependency>
				<groupId>com.coohua.caf</groupId>
				<artifactId>caf-boot-starter-logging</artifactId>
				<version>2.0.12-SNAPSHOT</version>
			</dependency>
			<dependency>
				<groupId>com.coohua.caf</groupId>
				<artifactId>caf-boot-starter-rpc</artifactId>
				<version>2.0.12-SNAPSHOT</version>
			</dependency>
			<dependency>
				<groupId>com.coohua.caf</groupId>
				<artifactId>caf-boot-starter-apollo</artifactId>
				<version>2.0.12-SNAPSHOT</version>
			</dependency>
			<dependency>
				<groupId>com.coohua.caf</groupId>
				<artifactId>caf-boot-starter-elasticsearch</artifactId>
				<version>2.0.12-SNAPSHOT</version>
			</dependency>
			<dependency>
				<groupId>com.coohua.caf</groupId>
				<artifactId>caf-boot-starter-rocketmq</artifactId>
				<version>2.0.12-SNAPSHOT</version>
			</dependency>
			<!-- Json -->
			<dependency>
				<groupId>com.alibaba</groupId>
				<artifactId>fastjson</artifactId>
				<version>${fastjson.version}</version>
			</dependency>
			<!-- JSON end -->
			<dependency>
				<groupId>com.lmax</groupId>
				<artifactId>disruptor</artifactId>
				<version>${lmax.disruptor.version}</version>
			</dependency>
			<dependency>
				<groupId>com.conversantmedia</groupId>
				<artifactId>disruptor</artifactId>
				<version>${conversantmedia.disruptor.version}</version>
			</dependency>
			<!-- mysql connector -->
			<dependency>
				<groupId>mysql</groupId>
				<artifactId>mysql-connector-java</artifactId>
				<version>${mysql-connector-java.version}</version>
			</dependency>
			<!-- druid db connector pool -->
			<dependency>
				<groupId>com.alibaba</groupId>
				<artifactId>druid</artifactId>
				<version>${druid.version}</version>
			</dependency>
			<!-- mybatis dao -->
			<dependency>
				<groupId>org.mybatis</groupId>
				<artifactId>mybatis</artifactId>
				<version>${mybatis.version}</version>
			</dependency>
			<dependency>
				<groupId>org.mybatis</groupId>
				<artifactId>mybatis-spring</artifactId>
				<version>${mybatis-spring.version}</version>
			</dependency>
			<dependency>
				<groupId>org.mybatis.spring.boot</groupId>
				<artifactId>mybatis-spring-boot-starter</artifactId>
				<version>${mybatis-spring.version}</version>
			</dependency>
			<!-- https://mvnrepository.com/artifact/org.apache.commons/commons-collections4 -->
			<dependency>
				<groupId>com.google.guava</groupId>
				<artifactId>guava</artifactId>
				<version>${guava.version}</version>
			</dependency>
			<dependency>
				<groupId>joda-time</groupId>
				<artifactId>joda-time</artifactId>
				<version>${joda-time.version}</version>
			</dependency>
			<dependency>
				<groupId>org.joda</groupId>
				<artifactId>joda-convert</artifactId>
				<version>${joda-convert.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.commons</groupId>
				<artifactId>commons-collections4</artifactId>
				<version>${commons-collections4.version}</version>
			</dependency>
			<dependency>
				<groupId>com.weibo</groupId>
				<artifactId>motan-core</artifactId>
				<version>${com.weibo.motan}</version>
				<exclusions>
					<exclusion>
						<artifactId>slf4j-log4j12</artifactId>
						<groupId>org.slf4j</groupId>
					</exclusion>
					<exclusion>
						<artifactId>slf4j-api</artifactId>
						<groupId>org.slf4j</groupId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>com.weibo</groupId>
				<artifactId>motan-transport-netty</artifactId>
				<version>${com.weibo.motan}</version>
				<exclusions>
					<exclusion>
						<artifactId>slf4j-log4j12</artifactId>
						<groupId>org.slf4j</groupId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>com.weibo</groupId>
				<artifactId>motan-springsupport</artifactId>
				<version>${com.weibo.motan}</version>
				<exclusions>
					<exclusion>
						<artifactId>slf4j-log4j12</artifactId>
						<groupId>org.slf4j</groupId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>com.weibo</groupId>
				<artifactId>motan-registry-zookeeper</artifactId>
				<version>${com.weibo.motan}</version>
				<exclusions>
					<exclusion>
						<artifactId>slf4j-log4j12</artifactId>
						<groupId>org.slf4j</groupId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>org.slf4j</groupId>
				<artifactId>slf4j-api</artifactId>
				<version>${slf4j.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.logging.log4j</groupId>
				<artifactId>log4j-slf4j-impl</artifactId>
				<version>2.10.0</version>
			</dependency>
			<dependency>
				<groupId>com.netflix.hystrix</groupId>
				<artifactId>hystrix-core</artifactId>
				<version>${hystrix.version}</version>
			</dependency>
			<dependency>
				<groupId>com.netflix.hystrix</groupId>
				<artifactId>hystrix-serialization</artifactId>
				<version>${hystrix.version}</version>
			</dependency>
			<dependency>
				<groupId>com.netflix.hystrix</groupId>
				<artifactId>hystrix-metrics-event-stream</artifactId>
				<version>${hystrix.version}</version>
			</dependency>
			<dependency>
				<groupId>com.netflix.hystrix</groupId>
				<artifactId>hystrix-javanica</artifactId>
				<version>${hystrix.version}</version>
			</dependency>

			<dependency>
				<groupId>com.ctrip.framework.apollo</groupId>
				<artifactId>apollo-client</artifactId>
				<version>${apollo-client.version}</version>
			</dependency>
			<dependency>
				<groupId>org.projectlombok</groupId>
				<artifactId>lombok</artifactId>
				<version>${lombok.version}</version>
			</dependency>
			<!-- prometheus -->
			<dependency>
				<groupId>io.prometheus</groupId>
				<artifactId>simpleclient</artifactId>
				<version>${prometheus.client.version}</version>
			</dependency>
			<dependency>
				<groupId>io.prometheus</groupId>
				<artifactId>simpleclient_hotspot</artifactId>
				<version>${prometheus.client.version}</version>
			</dependency>
			<dependency>
				<groupId>io.prometheus</groupId>
				<artifactId>simpleclient_httpserver</artifactId>
				<version>${prometheus.client.version}</version>
			</dependency>
			
			<!-- aliyun-oss -->
			<dependency>
	            <groupId>com.aliyun.oss</groupId>
	            <artifactId>aliyun-sdk-oss</artifactId>
	            <version>${aliyun.oss.client.version}</version>
	        </dependency>
	        
	        
	        <!-- aliyun-cdn -->
	        <dependency>
			    <groupId>com.aliyun</groupId>
			    <artifactId>aliyun-java-sdk-cdn</artifactId>
			    <version>${aliyun.java.sdk.cdn.version}</version>
			</dependency>
			
			<dependency>
			    <groupId>com.aliyun</groupId>
			    <artifactId>aliyun-java-sdk-core</artifactId>
			    <version>${aliyun.java.sdk.core.version}</version>
			</dependency>

			<!-- rocketmq -->
			<dependency>
				<groupId>org.apache.rocketmq</groupId>
				<artifactId>rocketmq-client</artifactId>
				<version>${rocketmq.client.version}</version>
			</dependency>

			<!-- elasticsearch -->
<!-- 			<dependency> -->
<!-- 			    <groupId>org.elasticsearch</groupId> -->
<!-- 			    <artifactId>elasticsearch</artifactId> -->
<!-- 			    <version>${elasticsearch.client.version}</version> -->
<!-- 			</dependency> -->
<!-- 			<dependency> -->
<!-- 				<groupId>org.springframework.boot</groupId> -->
<!-- 				<artifactId>spring-boot-starter-data-elasticsearch</artifactId> -->
<!-- 				<version>2.1.2.RELEASE</version> -->
<!-- 			</dependency> -->
			<dependency>
				<groupId>org.springframework.data</groupId>
				<artifactId>spring-data-elasticsearch</artifactId>
				<version>3.1.4.RELEASE</version>
			</dependency>
		</dependencies>
	</dependencyManagement>
	<distributionManagement>
		<repository>
			<id>coohua_releases</id>
			<name>releases</name>
			<url>http://maven.coohua.com:8002/nexus/content/repositories/releases</url>
		</repository>
		<snapshotRepository>
			<id>coohua_snapshots</id>
			<name>snapshots</name>
			<url>http://maven.coohua.com:8002/nexus/content/repositories/snapshots</url>
		</snapshotRepository>
	</distributionManagement>
	<repositories>
		<repository>
			<id>coohua_releases</id>
			<url>http://maven.coohua.com:8002/nexus/content/repositories/releases</url>
			<releases>
				<enabled>true</enabled>
			</releases>
			<snapshots>
				<enabled>false</enabled>
			</snapshots>
		</repository>
		<repository>
			<id>coohua_snapshots</id>
			<url>http://maven.coohua.com:8002/nexus/content/repositories/snapshots</url>
			<releases>
				<enabled>true</enabled>
			</releases>
			<snapshots>
				<enabled>true</enabled>
			</snapshots>
		</repository>
		<repository>
			<id>coohua_3rdparty</id>
			<url>http://maven.coohua.com:8002/nexus/content/repositories/thirdparty/</url>
			<releases>
				<enabled>true</enabled>
			</releases>
			<snapshots>
				<enabled>false</enabled>
			</snapshots>
		</repository>
	</repositories>
	<build>
		<plugins>
			<plugin>
				<artifactId>maven-source-plugin</artifactId>
				<configuration>
					<attach>true</attach>
				</configuration>
				<executions>
					<execution>
						<id>attach-sources</id>
						<goals>
							<goal>jar-no-fork</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<configuration>
					<source>${java.version}</source>
					<target>${java.version}</target>
					<encoding>${project.build.sourceEncoding}</encoding>
					<showWarnings>true</showWarnings>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-resources-plugin</artifactId>
				<configuration>
					<encoding>${project.build.sourceEncoding}</encoding>
				</configuration>
			</plugin>
			<!--<plugin> -->
			<!--<groupId>org.apache.maven.plugins</groupId> -->
			<!--<artifactId>maven-dependency-plugin</artifactId> -->
			<!--<executions> -->
			<!--<execution> -->
			<!--<id>copy</id> -->
			<!--<phase>install</phase> -->
			<!--<goals> -->
			<!--<goal>copy-dependencies</goal> -->
			<!--</goals> -->
			<!--<configuration> -->
			<!--<outputDirectory> -->
			<!--${project.build.directory}/lib -->
			<!--</outputDirectory> -->
			<!--<includeScope> -->
			<!--runtime -->
			<!--</includeScope> -->
			<!--</configuration> -->
			<!--</execution> -->
			<!--</executions> -->
			<!--</plugin> -->
			<plugin>
				<groupId>org.codehaus.mojo</groupId>
				<artifactId>flatten-maven-plugin</artifactId>
				<configuration>
					<updatePomFile>true</updatePomFile>
					<flattenMode>bom</flattenMode>
				</configuration>
				<executions>
					<execution>
						<id>flatten</id>
						<phase>process-resources</phase>
						<goals>
							<goal>flatten</goal>
						</goals>
					</execution>
					<execution>
						<id>flatten.clean</id>
						<phase>clean</phase>
						<goals>
							<goal>clean</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>
</project>
