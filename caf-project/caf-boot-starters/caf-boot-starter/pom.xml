<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <artifactId>caf-boot-starters</artifactId>
        <groupId>com.coohua.caf</groupId>
        <version>2.0.12-SNAPSHOT</version>
    </parent>

    <artifactId>caf-boot-starter</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.coohua.caf</groupId>
            <artifactId>caf-boot-starter-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.coohua.caf</groupId>
            <artifactId>caf-boot-starter-kv</artifactId>
        </dependency>
        <dependency>
            <groupId>com.coohua.caf</groupId>
            <artifactId>caf-boot-starter-rpc</artifactId>
        </dependency>
        <dependency>
            <groupId>com.coohua.caf</groupId>
            <artifactId>caf-boot-starter-db</artifactId>
        </dependency>
        <dependency>
            <groupId>com.coohua.caf</groupId>
            <artifactId>caf-boot-starter-apollo</artifactId>
        </dependency>
        <dependency>
            <groupId>com.coohua.caf</groupId>
            <artifactId>caf-core</artifactId>
        </dependency>
        <dependency>
	      <groupId>com.coohua.caf</groupId>
	      <artifactId>caf-boot-starter-rocketmq</artifactId>
	    </dependency>
	    <dependency>
	      <groupId>com.coohua.caf</groupId>
	      <artifactId>caf-boot-starter-elasticsearch</artifactId>
	    </dependency>
        <!-- Test -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>
</project>
