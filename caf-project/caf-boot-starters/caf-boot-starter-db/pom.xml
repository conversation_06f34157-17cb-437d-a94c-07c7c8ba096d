<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <artifactId>caf-boot-starters</artifactId>
        <groupId>com.coohua.caf</groupId>
        <version>2.0.12-SNAPSHOT</version>
    </parent>

    <artifactId>caf-boot-starter-db</artifactId>

    <dependencies>
        <dependency>
            <groupId>top.zrbcool</groupId>
            <artifactId>pepper-metrics-mybatis</artifactId>
        </dependency>
        <dependency>
            <groupId>top.zrbcool</groupId>
            <artifactId>pepper-metrics-druid</artifactId>
        </dependency>
        <dependency>
            <groupId>com.coohua.caf</groupId>
            <artifactId>caf-core</artifactId>
        </dependency>
        <!-- mysql connector -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>
        <!-- druid db connector pool -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis-spring</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-jdbc</artifactId>
        </dependency>
    </dependencies>
</project>