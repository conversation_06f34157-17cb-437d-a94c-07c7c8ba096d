<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <artifactId>caf-boot-starters</artifactId>
        <groupId>com.coohua.caf</groupId>
        <version>2.0.12-SNAPSHOT</version>
    </parent>

    <artifactId>caf-boot-starter-rpc</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.coohua.caf</groupId>
            <artifactId>caf-core</artifactId>
        </dependency>
        <!-- motan -->
        <dependency>
            <groupId>top.zrbcool</groupId>
            <artifactId>pepper-metrics-motan</artifactId>
        </dependency>
        <dependency>
            <groupId>com.weibo</groupId>
            <artifactId>motan-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.weibo</groupId>
            <artifactId>motan-transport-netty</artifactId>
        </dependency>
        <dependency>
            <groupId>com.weibo</groupId>
            <artifactId>motan-springsupport</artifactId>
        </dependency>
        <dependency>
            <groupId>com.weibo</groupId>
            <artifactId>motan-registry-zookeeper</artifactId>
        </dependency>
    </dependencies>
</project>
