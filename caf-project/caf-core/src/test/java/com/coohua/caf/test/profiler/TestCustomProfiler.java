package com.coohua.caf.test.profiler;

import com.coohua.caf.test.aop.TestSpringBootApplication;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = TestCustomProfiler.Application.class)
public class TestCustomProfiler {

    @Autowired
    private MockService mockService;

    @Test
    public void test() {
        ExecutorService executorService = Executors.newSingleThreadExecutor();
        executorService.submit(new Runnable() {
            @Override
            public void run() {
                for (;;) {
                    try {
                        mockService.doSomeThing2();
                        mockService.doSomeThing();
                        mockService.doSomeThing1();
                    } catch (Throwable throwable) {
                        log.error("", throwable);
                    }
                }
            }
        });
        for (;;);
    }

    @SpringBootApplication
    @Import(Config.class)
    public static class Application {
        public static void main(String[] args) {
            SpringApplication.run(TestSpringBootApplication.class);
        }
    }
}
