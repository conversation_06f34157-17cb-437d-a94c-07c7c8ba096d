package com.coohua.caf.test.web;

import com.coohua.caf.core.logging.Loggers;
import com.coohua.caf.core.util.HttpUtil;
import io.prometheus.client.Collector;
import io.prometheus.client.CollectorRegistry;
import io.prometheus.client.Summary;
import io.prometheus.client.exporter.HTTPServer;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.slf4j.Logger;

import java.io.IOException;
import java.net.URL;
import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * Created by zhangrongbin on 2018/11/06.
 */
public class TestHttp {
    private static Logger log = Loggers.getFrameworkLogger();
    @Test
    public void urlHandle() {
        try {
            URL url = new URL("http://www.runoob.com/user/1234?language=cn#j2se");
            System.out.println("URL 为：" + url.toString());
            System.out.println("协议为：" + url.getProtocol());
            System.out.println("验证信息：" + url.getAuthority());
            System.out.println("文件名及请求参数：" + url.getFile());
            System.out.println("主机名：" + url.getHost());
            System.out.println("路径：" + url.getPath());
            System.out.println("端口：" + url.getPort());
            System.out.println("默认端口：" + url.getDefaultPort());
            System.out.println("请求参数：" + url.getQuery());
            System.out.println("定位位置：" + url.getRef());
            System.out.println(HttpUtil.getUrlWithoutNumber(url.toString()));
        } catch (IOException e) {
            log.error("", e);
        }
    }

    @Test
    public void testPrometheus() throws InterruptedException, IOException {
        CollectorRegistry registry = new CollectorRegistry();
        Summary APP_HTTP_REQUESTS_LATENCY_SECONDS = Summary.build()
                .name("app_http_requests_latency_seconds")
                .help("app_http_requests_latency_seconds")
                .maxAgeSeconds(30)
                .labelNames("url")
                .quantile(0.5, 0.05)
                .quantile(0.9, 0.01)
                .quantile(0.99, 0.01)
                .quantile(0.999, 0.01)
                .register();
        Executors.newSingleThreadScheduledExecutor().scheduleAtFixedRate(() -> {
            List<Collector.MetricFamilySamples> familySamples = APP_HTTP_REQUESTS_LATENCY_SECONDS.collect();
            for (Collector.MetricFamilySamples samples : familySamples) {
                for (Collector.MetricFamilySamples.Sample sample : samples.samples) {
                    if (StringUtils.endsWith(sample.name, "_count") ||
                            StringUtils.endsWith(sample.name, "_sum")) {
                        System.out.println(String.format("%s=%s %s=%s", sample.labelNames.get(0), sample.labelValues.get(0), sample.name, sample.value));
                    } else {
                        System.out.println(String.format("%s=%s %s=%s %s", sample.labelNames.get(0), sample.labelValues.get(0), sample.labelNames.get(1), sample.labelValues.get(1), sample.value * 1000));
                    }
                }
            }
        }, 10, 10, TimeUnit.SECONDS);
        while (true) {
            Summary.Timer timer = APP_HTTP_REQUESTS_LATENCY_SECONDS.labels("/api/news").startTimer();
            try {
                TimeUnit.MILLISECONDS.sleep(RandomUtils.nextInt(30, 50));
            } finally {
                timer.observeDuration();
            }
        }
    }


    @Test
    public void testNull() {
        Integer integer = (Integer)null;
        System.out.println(integer);
    }
}
