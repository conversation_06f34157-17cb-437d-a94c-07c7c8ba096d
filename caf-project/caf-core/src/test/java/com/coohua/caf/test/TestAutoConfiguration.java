package com.coohua.caf.test;

import com.coohua.caf.core.web.FastJsonAutoConfiguration;
import org.junit.Test;
import org.springframework.boot.autoconfigure.AutoConfigurations;
import org.springframework.boot.autoconfigure.http.HttpMessageConverters;
import org.springframework.boot.test.context.runner.ApplicationContextRunner;
import static org.assertj.core.api.Assertions.*;

/**
 *
 */
public class TestAutoConfiguration {
    @Test
    public void defaultFastJsonAutoConfiguration() {
        ApplicationContextRunner contextRunner = new ApplicationContextRunner()
                .withConfiguration(AutoConfigurations.of(FastJsonAutoConfiguration.class));
        contextRunner.run((context -> assertThat(context).hasSingleBean(HttpMessageConverters.class)));
    }
}
