package com.coohua.caf.test.aop;

import org.apache.commons.lang3.RandomUtils;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2018/11/15.
 */
@Component
public class MockJedisClient {
    public String set(String key, String value) {
        try {
            TimeUnit.NANOSECONDS.sleep(RandomUtils.nextInt(100000, 5000000));
            //TimeUnit.MILLISECONDS.sleep(RandomUtils.nextInt(100, 1500));
        } catch (InterruptedException ignored) {
        }
        return "done";
    }
}
