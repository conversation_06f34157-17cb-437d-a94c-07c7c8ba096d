package com.coohua.caf.test;

import org.apache.logging.log4j.core.LoggerContext;
import org.apache.logging.log4j.core.config.ConfigurationSource;
import org.apache.logging.log4j.core.config.Configurator;
import org.junit.BeforeClass;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;

/**
 * Created by zhangrongbin on 2018/11/15.
 */
public class BaseTest {
    @BeforeClass
    public static void setup() throws FileNotFoundException {
        File config = new File("/home/<USER>/git-coohua/caf/caf-project/caf-core/src/test/resources/log4j2.xml");
        ConfigurationSource source = new ConfigurationSource(new FileInputStream(config),config);
        final LoggerContext initialize = Configurator.initialize(null, source);
        initialize.start();
    }
}
