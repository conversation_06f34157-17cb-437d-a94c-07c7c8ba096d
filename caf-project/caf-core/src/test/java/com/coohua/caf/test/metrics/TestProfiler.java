package com.coohua.caf.test.metrics;

import com.coohua.caf.core.kv.JedisClient;
import com.coohua.caf.core.metrics.LatencyProfiler;
import com.coohua.caf.core.metrics.LatencyStat;
import com.coohua.caf.core.metrics.LatencyStatPrinter;
import com.coohua.caf.core.metrics.MonitorConfig;
import com.coohua.caf.test.BaseTest;
import com.weibo.api.motan.transport.async.MotanAsyncProcessor;
import io.prometheus.client.exporter.HTTPServer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomUtils;
import org.junit.Test;
import sun.security.jca.Providers;

import javax.sql.DataSource;
import java.io.IOException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Created by z<PERSON><PERSON>bin on 2018/11/08.
 */
@Slf4j
public class TestProfiler extends BaseTest {
    private static final ScheduledExecutorService EXECUTOR = Executors.newSingleThreadScheduledExecutor();
    private AtomicInteger counterOne = new AtomicInteger();

//    public ReactorClientHttpConnector connector = new ReactorClientHttpConnector(
//            options -> options.option(ChannelOption.CONNECT_TIMEOUT_MILLIS, 50)
//                    .compression(true)
//                    .afterNettyContextInit(ctx -> {
//                        ctx.addHandlerLast(new ReadTimeoutHandler(300, TimeUnit.MILLISECONDS));
//                    }));

//    public WebClient webclient = WebClient.builder()
//            .clientConnector(connector)
//            .build();


//    @Test
//    public void testAsyncProfiler() {
//        MonitorConfig monitorConfig = new MonitorConfig();
//        monitorConfig.setLogDelay(10);
//        monitorConfig.setLogPeriod(60);
//        try {
//            new HTTPServer(9145);
//        } catch (IOException e) {
//
//        }
//        EXECUTOR.scheduleAtFixedRate(
//                new LatencyStatPrinter(monitorConfig),
//                monitorConfig.getLogDelay(),
//                monitorConfig.getLogPeriod(),
//                TimeUnit.SECONDS
//        );
//
//        final LatencyStat httpStat = LatencyProfiler.Builder.build()
//                .name("app_http_requests")
//                .defineLabels("url", "endpoint")
//                .tag("http:outgoing")
//                .create();
//
//        final ExecutorService fixedThreadPool = Executors.newFixedThreadPool(20);
//        for (int i = 0; i < 10; i++) {
//            fixedThreadPool.submit(() -> {
//                for (;;) {
//                    asyncMockWebflux(httpStat, "/api/news", "/api");
//                }
//            });
//        }
//        for (int i = 0; i < 10; i++) {
//            fixedThreadPool.submit(() -> {
//                for (;;) {
//                    asyncMockWebflux(httpStat, "/task/refresh", "/task");
//                }
//            });
//        }
//        for (;;) {
//            try {
//                TimeUnit.MILLISECONDS.sleep(100);
//            } catch (InterruptedException e) {
//            }
//        }
//    }

//    private void asyncMockWebflux(LatencyStat httpStat, String metrics, String category) throws Exception {
//        final LatencyStat.Timer[] timer = {null};
//        final Mono<String> stringMono = webclient.get().uri("http://www.baidu.com")
//                .header("accept-encoding", "gzip, deflate")
//                .header("connection", "keep-alive")
//                .header("cache-control", "no-cache")
//                .retrieve()
//                .bodyToMono(String.class)
//                .doOnRequest(n -> {
//                    httpStat.inc(metrics, category);
//                    timer[0] = httpStat.startTimer(metrics, category);
//                })
//                .doOnSuccess(n -> {
//                    timer[0].observeDuration();
//                    httpStat.dec(metrics, category);
//                })
//                .doOnError(n -> {
//                    timer[0].observeDuration();
//                    httpStat.dec(metrics, category);
//                    httpStat.error(metrics, category);
//                })
//                .map(res -> {
//                    try {
//                        TimeUnit.MILLISECONDS.sleep(RandomUtils.nextInt(50, 100));
//                    } catch (InterruptedException e) {
//                    }
//                    return res;
//                }).onErrorResume(throwable -> Mono.just("null error")).doAfterTerminate(() -> {});//.toFuture().get();
//        stringMono.block();
//    }

//    private void asyncMock(LatencyStat httpStat, String metrics, String category) {
//        final LatencyStat.Timer timer = httpStat.startTimer(metrics, category);
//        List<Integer> stream = Lists.newArrayList();
//        stream.add(1);
//        stream.parallelStream().map(it -> {
//            try {
//                httpStat.inc(metrics, category);
//                if (counterOne.incrementAndGet() % 100 > 95) {
//                    TimeUnit.MILLISECONDS.sleep(RandomUtils.nextInt(50, 100));
//                } else {
//                    TimeUnit.SECONDS.sleep(1);
//                    throw new IllegalArgumentException();
//                }
//                //log.info("dealed:" + it);
//                return "dealed:" + it;
//            } catch (Exception ignored) {
//                httpStat.error(metrics, category);
//            }
//            return "dealed" + it;
//        }).forEach(it -> {
//            //log.info("observeDuration:" + it);
//            httpStat.dec(metrics, category);
//            timer.observeDuration();
//        });
//    }


    @Test
    public void testProfiler() {
        try {
            new HTTPServer(9145);
        } catch (IOException e) {

        }
        MonitorConfig monitorConfig = new MonitorConfig();
        monitorConfig.setLogDelay(1);
        monitorConfig.setLogPeriod(1);
        EXECUTOR.scheduleAtFixedRate(
                new LatencyStatPrinter(monitorConfig),
                monitorConfig.getLogDelay(),
                monitorConfig.getLogPeriod(),
                TimeUnit.SECONDS
        );
        final LatencyStat httpStat = LatencyProfiler.Builder.build()
                .name("app_http_requests")
                .defineLabels("url", "endpoint")
                .tag("http:incoming")
                .create();
        final LatencyStat motanStat = LatencyProfiler.Builder.build()
                .name("app_motan_requests")
                .defineLabels("method", "service")
                .tag("motan:incoming")
                .create();
        final ExecutorService executorService = Executors.newFixedThreadPool(100);
        for (;;) {
            httpStat.observe("/api/news", "/api", RandomUtils.nextLong(100L, 120L), TimeUnit.MILLISECONDS);
            httpStat.observe("/task/refresh", "/task", RandomUtils.nextLong(80L, 120L), TimeUnit.MILLISECONDS);
            httpStat.observe("/master/addGold", "/master", RandomUtils.nextLong(50L, 120L), TimeUnit.MILLISECONDS);
            httpStat.observe("/gold/add", "/gold", RandomUtils.nextLong(20L, 120L), TimeUnit.MILLISECONDS);

            motanStat.observe(Providers.class.getMethods()[0].getName(), Providers.class.getSimpleName(), RandomUtils.nextLong(50L, 120L), TimeUnit.MILLISECONDS);
            motanStat.observe(DataSource.class.getMethods()[0].getName(), DataSource.class.getSimpleName(), RandomUtils.nextLong(60L, 120L), TimeUnit.MILLISECONDS);
            motanStat.observe(MotanAsyncProcessor.class.getMethods()[0].getName(), MotanAsyncProcessor.class.getSimpleName(), RandomUtils.nextLong(100L, 120L), TimeUnit.MILLISECONDS);
            motanStat.observe(JedisClient.class.getMethods()[0].getName(), JedisClient.class.getSimpleName(), RandomUtils.nextLong(100L, 120L), TimeUnit.MILLISECONDS);

            executorService.submit(() -> {
                httpStat.inc("/gold/add", "/gold");
                final LatencyStat.Timer timer = httpStat.startTimer("/gold/add", "/gold");
                motanStat.inc(JedisClient.class.getMethods()[0].getName(), JedisClient.class.getSimpleName());
                if (RandomUtils.nextBoolean()) {
                    httpStat.error("/gold/add", "/gold");
                    motanStat.error(JedisClient.class.getMethods()[0].getName(), JedisClient.class.getSimpleName());
                }
                try {
                    TimeUnit.MILLISECONDS.sleep(3000);
                } catch (InterruptedException ignored) {
                } finally {
                    timer.observeDuration();
                    httpStat.dec("/gold/add", "/gold");
                    motanStat.dec(JedisClient.class.getMethods()[0].getName(), JedisClient.class.getSimpleName());
                }
            });
            try {
                TimeUnit.MILLISECONDS.sleep(20);
            } catch (InterruptedException ignored) {
            }
        }
    }
}
