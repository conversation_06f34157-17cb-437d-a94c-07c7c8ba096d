package com.coohua.caf.test.jedis;

import io.lettuce.core.cluster.RedisClusterClient;
import io.lettuce.core.cluster.api.StatefulRedisClusterConnection;
import io.lettuce.core.cluster.api.sync.RedisAdvancedClusterCommands;
import org.junit.Test;
import org.springframework.util.Assert;
import redis.clients.jedis.HostAndPort;
import redis.clients.jedis.JedisCluster;

import java.util.HashSet;
import java.util.Set;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2018/09/30.
 */
public class TestJedisCluster {
    @Test
    public void testJedisCluster() {
        Set<HostAndPort> nodes = new HashSet<>();
        nodes.add(new HostAndPort("***************", 7000));
        nodes.add(new HostAndPort("***************", 7001));
        nodes.add(new HostAndPort("***************", 7002));
        JedisCluster jedis = new JedisCluster(nodes);
        String hello = jedis.get("hello");
        System.out.println(hello);
    }

    @Test
    public void testLettuceCluster() {
        RedisClusterClient redisClient = RedisClusterClient.create("redis://@***************:7000");
        StatefulRedisClusterConnection<String, String> connection = redisClient.connect();
        System.out.println("Connected to Redis");
        RedisAdvancedClusterCommands<String, String> sync = connection.sync();
        sync.set("hello", "lettuce");
        String hello = sync.get("hello");
        Assert.isTrue("lettuce".equals(hello), "NOT OK");
        System.out.println(hello);
        connection.close();
        redisClient.shutdown();
    }
}
