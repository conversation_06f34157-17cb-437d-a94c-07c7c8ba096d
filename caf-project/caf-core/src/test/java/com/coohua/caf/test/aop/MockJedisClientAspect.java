package com.coohua.caf.test.aop;

import com.coohua.caf.core.metrics.LatencyProfiler;
import com.coohua.caf.core.metrics.LatencyStat;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

/**
 * Created by zhangrongbin on 2018/11/15.
 */
@Component
@Aspect
@Slf4j
public class MockJedisClientAspect {
    private static final LatencyStat LATENCY_STAT = LatencyProfiler.Builder.build()
            .tag("jedis")
            .name("jedis")
            .defineLabels("operation", "class")
            .buckets(0.0001, 0.0005, 0.001, 0.002, 0.005, 0.01, 0.02, 0.05, 0.1, 0.5, 1, 2, 5)
            .create();
    //@Pointcut("execution(public * com.coohua.caf.test.aop.MockJedisClient.set())")
    @Pointcut("execution(* com.coohua.caf.test.aop.MockJedisClient.*(..))")
    public void ops(){}

    @Around("ops()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        final String methodName = joinPoint.getSignature().getName();
        final String declaringTypeName = joinPoint.getSignature().getDeclaringTypeName();
        final LatencyStat.Timer timer = LATENCY_STAT.startTimer(methodName, declaringTypeName);
        try {
            LATENCY_STAT.inc(methodName, declaringTypeName);
            return joinPoint.proceed();
        } catch (Throwable throwable) {
            LATENCY_STAT.error(methodName, declaringTypeName);
            throw throwable;
        } finally {
            timer.observeDuration();
            LATENCY_STAT.dec(methodName, declaringTypeName);
        }
    }

}
