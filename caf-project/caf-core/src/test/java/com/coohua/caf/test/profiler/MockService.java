package com.coohua.caf.test.profiler;

import com.coohua.caf.core.metrics.CustomProfiler;
import org.apache.commons.lang3.RandomUtils;

import java.util.concurrent.TimeUnit;

public class MockService {
    public void doSomeThing() throws Throwable {
        /**
         * 场景1：
         * 方法1调用方法2，想profile方法2
         */
        CustomProfiler.Procedure procedure = CustomProfiler.beginProcedure("doSomeThingElse", this.getClass());
        try {
            doSomeThingElse();
        } catch (Exception e) {
            procedure.exception(e);
        } finally {
            procedure.complete();
        }
    }

    public void doSomeThing1() throws Throwable {
        /**
         * 场景2：
         * 我只想profile一个代码块
         */

        CustomProfiler.Procedure procedure = CustomProfiler.beginProcedure("loop-100-times", this.getClass());
        try {
            /**
             * 你的逻辑 -> here
             * 这里的例子是：
             *      任性循环一百次, 心情不好就丢异常
             */
            for (int i = 0; i < 100; i++) {
                TimeUnit.MILLISECONDS.sleep(1);
            }
            if (RandomUtils.nextInt(0, 100) % 5 == 0) {
                throw new RuntimeException("不开心！");
            }
        } catch (Exception e) {
            procedure.exception(e);
        } finally {
            procedure.complete();
        }
    }

    public void doSomeThing2() {
        try {
            aMethod();
        } catch (Throwable ignore){}
    }

    public static void aMethod() throws Throwable {
        CustomProfiler.Procedure procedure = CustomProfiler.beginProcedure();
        try {
            // 任性循环一百次, 心情不好就丢异常
            for (int i = 0; i < 100; i++) {
                TimeUnit.MILLISECONDS.sleep(1);
            }
            if (RandomUtils.nextInt(0, 100) % 5 == 0) {
                throw new RuntimeException("不开心！");
            }
        } catch (Exception e) {
            procedure.exception(e);
        } finally {
            procedure.complete();
        }
    }

    private void doSomeThingElse() throws InterruptedException {
        // 任性循环一百次, 心情不好就丢异常
        for (int i = 0; i < 100; i++) {
            TimeUnit.MILLISECONDS.sleep(1);
        }
        if (RandomUtils.nextInt(0, 100) % 5 == 0) {
            throw new RuntimeException("不开心！");
        }
    }
}
