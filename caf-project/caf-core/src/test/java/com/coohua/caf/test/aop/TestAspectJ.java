package com.coohua.caf.test.aop;

import com.coohua.caf.test.BaseTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * Created by zhangrongbin on 2018/11/15.
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = TestSpringBootApplication.class)
public class TestAspectJ extends BaseTest {
    @Autowired
    private MockJedisClient jedisClient;
    @Test
    public void testAopWithAspectJ() {
        final int size = 10;
        final ExecutorService executorService = Executors.newFixedThreadPool(size);
        for (int i = 0; i < size; i++) {
            executorService.submit(() -> {
                for (;;) {
                    jedisClient.set("hello", "robin");
                }
            });
        }

        for (;;);
    }
}
