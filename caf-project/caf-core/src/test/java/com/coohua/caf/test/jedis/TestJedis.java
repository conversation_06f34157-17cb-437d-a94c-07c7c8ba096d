package com.coohua.caf.test.jedis;

import com.alibaba.fastjson.TypeReference;
import com.coohua.caf.core.kv.AbstractJedisConfiguration;
import com.coohua.caf.core.kv.JedisClient;
import com.coohua.caf.core.kv.RedisClient;
import org.apache.commons.lang3.RandomUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;

import java.io.Serializable;
import java.util.*;

/**
 * Created by zhangrong<PERSON> on 2018/09/29.
 */

@RunWith(SpringRunner.class)
//@SpringBootTest
public class TestJedis extends AbstractJedisConfiguration {
    private static final Logger logger = LoggerFactory.getLogger(TestJedis.class);
    private RedisClient<Jedis> redisClient;
//    @Before
//    public void setUp() {
//        this.redisClient = jedisClient(jedisPool(jedisPoolConfig()));
//    }

    @Test
    public void testJedisClient() {
        redisClient.set("jedis", "ok");
        String jedis = redisClient.get("jedis");
        logger.info(jedis);
        Assert.assertEquals("ok", jedis);
    }

    @Test
    public void testCache() {
        User user = new User();
        user.setName("robin");
        user.setAge(18);
        user.getAddressList().add(new Address(10010, "北京"));
        user.getAddressList().add(new Address(10012, "长春"));
        logger.info("set cache to redis: {}", user);
        redisClient.setCache("testCache", user);

        User userFromCache = redisClient.getCache("testCache", new TypeReference<User>(){});
        logger.info("read cache to redis: {}", userFromCache);
    }

    @Test
    public void testCachePrimitive() {
        int intTo = 100;
        redisClient.setCache("intTo", intTo);
        Integer intFrom = redisClient.getCache("intTo", new TypeReference<Integer>() {});
        logger.info("to: {} --> from {}", intTo, intFrom);
        Assert.assertEquals(intTo, intFrom.intValue());

        long longTo = 120L;
        redisClient.setCache("longTo", longTo);
        Long longFrom = redisClient.getCache("longTo", new TypeReference<Long>() {});
        logger.info("to: {} --> from {}", longTo, longFrom);
        Assert.assertEquals(longTo, longFrom.intValue());

        float fTo = 123.123f;
        redisClient.setCache("fTo", fTo);
        Float fFrom = redisClient.getCache("fTo", new TypeReference<Float>() {});
        logger.info("to: {} --> from {}", fTo, fFrom);
        Assert.assertEquals(fTo, fFrom, 0.0);

        double dTo = 321.321d;
        redisClient.setCache("dTo", dTo);
        Double dFrom = redisClient.getCache("dTo", new TypeReference<Double>() {});
        logger.info("to: {} --> from {}", dTo, dFrom);
        Assert.assertEquals(dTo, dFrom, 0.0);

        boolean booleanTo = false;
        redisClient.setCache("booleanTo", booleanTo);
        Boolean booleanFrom = redisClient.getCache("booleanTo", new TypeReference<Boolean>() {});
        logger.info("to: {} --> from {}", booleanTo, booleanFrom);
        Assert.assertEquals(booleanTo, booleanFrom);

        short sTo = 1111;
        redisClient.setCache("sTo", sTo);
        Short sFrom = redisClient.getCache("sTo", new TypeReference<Short>() {});
        logger.info("to: {} --> from {}", sTo, sFrom);
        Assert.assertEquals(sTo, sFrom.shortValue());

        byte byteTo = 111;
        redisClient.setCache("byteTo", byteTo);
        Byte byteFrom = redisClient.getCache("byteTo", new TypeReference<Byte>() {});
        logger.info("to: {} --> from {}", byteTo, byteFrom);
        Assert.assertEquals(byteTo, byteFrom.byteValue());

        char charTo = 'A';
        redisClient.setCache("charTo", charTo);
        Character charFrom = redisClient.getCache("charTo", new TypeReference<Character>() {});
        logger.info("to: {} --> from {}", charTo, charFrom);
        Assert.assertEquals(charTo, charFrom.charValue());

        // complex samples
        Map map = new HashMap();
        map.put("intTo", intTo);
        map.put("longTo", longTo);
        map.put("byteTo", byteTo);
        map.put("fTo", fTo);
        map.put("dTo", dTo);
        map.put("charTo", charTo);
        map.put("sTo", sTo);
        map.put("booleanTo", booleanTo);
        redisClient.setCache("mapWithPrimitive", map);
        Map mapFrom = redisClient.getCache("mapWithPrimitive", new TypeReference<Map>() {});
        logger.info("to  : {}", map);
        logger.info("from: {}", mapFrom);
        mapFrom.forEach((key, value) -> logger.info("key: {}, value: {}, type: {}", key, value, value.getClass()));

        //generic type
        Map<String, List<List<User>>> map2 = new HashMap<>();
        List<User> users = new ArrayList<>();
        users.add(User.createUser());
        users.add(User.createUser());
        List<User> users2 = new ArrayList<>();
        users.add(User.createUser());
        users.add(User.createUser());
        List<List<User>> list = new ArrayList<>();
        list.add(users);
        list.add(users2);
        map2.put("list", list);
        redisClient.setCache("genericMap", map2);
        Map<String, List<List<User>>> map2From = redisClient.getCache("genericMap", new TypeReference<Map<String, List<List<User>>>>() {});
        logger.info("to  : {}", map2);
        logger.info("from: {}", map2From);
        map2From.forEach((key, value) -> logger.info("key: {}, value: {}, type: {}", key, value, value.getClass()));
    }

    public JedisPoolConfig jedisPoolConfig() {
        return createJedisPoolConfig();
    }

    public JedisPool jedisPool(JedisPoolConfig jedisPoolConfig) {
        return new JedisPool(jedisPoolConfig, "************", 6379);
    }

//    public JedisClient jedisClient(JedisPool jedisPool) {
//        return new JedisClient(jedisPool);
//    }

    @Override
    protected String namespace() {
        return null;
    }

    @Override
    protected JedisClient jedisClient() {
        return null;
    }

    static class User implements Serializable {
        private String name;
        private int age;
        private List<Address> addressList = new ArrayList<>();
        private float money = 123.123f;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getAge() {
            return age;
        }

        public void setAge(int age) {
            this.age = age;
        }

        public List<Address> getAddressList() {
            return addressList;
        }

        public void setAddressList(List<Address> addressList) {
            this.addressList = addressList;
        }

        public float getMoney() {
            return money;
        }

        public void setMoney(float money) {
            this.money = money;
        }

        @Override
        public String toString() {
            return "User{" +
                    "name='" + name + '\'' +
                    ", age=" + age +
                    ", addressList=" + addressList +
                    ", money=" + money +
                    '}';
        }

        public static User createUser() {
            User user = new User();
            user.setName("robin-" + System.currentTimeMillis());
            user.setAge(RandomUtils.nextInt());
            user.getAddressList().add(new Address(RandomUtils.nextInt(), "北京"));
            user.getAddressList().add(new Address(RandomUtils.nextInt(), "长春"));
            return user;
        }
    }

    static class Address implements Serializable{
        private int postCode;
        private String address;

        public Address() {
        }

        public Address(int postCode, String address) {
            this.postCode = postCode;
            this.address = address;
        }

        public int getPostCode() {
            return postCode;
        }

        public void setPostCode(int postCode) {
            this.postCode = postCode;
        }

        public String getAddress() {
            return address;
        }

        public void setAddress(String address) {
            this.address = address;
        }

        @Override
        public String toString() {
            return "Address{" +
                    "postCode=" + postCode +
                    ", address='" + address + '\'' +
                    '}';
        }
    }

}
