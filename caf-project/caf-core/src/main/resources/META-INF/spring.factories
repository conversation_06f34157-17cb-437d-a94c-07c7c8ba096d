# Initializers
org.springframework.context.ApplicationContextInitializer=\
com.coohua.caf.core.logging.Log4j2ContextInitializer

org.springframework.boot.autoconfigure.EnableAutoConfiguration=\
com.coohua.caf.core.web.FastJsonAutoConfiguration,\
com.coohua.caf.core.base.CustomizedPropertiesBinderAutoConfiguration,\
com.coohua.caf.core.base.FrameworkConfigAutoConfiguration,\
com.coohua.caf.core.base.SpringApplicationEventListenerAutoConfiguration,\
com.coohua.caf.core.web.WebAutoConfiguration,\
com.coohua.caf.core.kv.JedisListenerAutoConfiguration,\
com.coohua.caf.core.kv.JedisAutoConfiguration,\
com.coohua.caf.core.kv.JedisClusterAutoConfiguration,\
com.coohua.caf.core.rpc.MotanAutoConfiguration,\
com.coohua.caf.core.rpc.MotanConfigListenerAutoConfiguration,\
com.coohua.caf.core.db.DataSourceListenerAutoConfiguration,\
com.coohua.caf.core.logging.LoggerLevelRefresherAutoConfiguration,\
com.coohua.caf.core.apollo.ApolloConfigAutoChangeAutoConfiguration,\
com.coohua.caf.core.sentinel.SentinelAutoConfiguration,\
com.coohua.caf.core.web.CHttpClientAutoConfiguration,\
com.coohua.caf.core.rocketmq.core.RocketMQAutoConfiguration