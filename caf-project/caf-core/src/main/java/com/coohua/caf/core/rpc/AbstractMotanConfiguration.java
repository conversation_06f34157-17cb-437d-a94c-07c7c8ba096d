package com.coohua.caf.core.rpc;

import com.weibo.api.motan.config.springsupport.*;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2018/09/29.
 */
public abstract class AbstractMotanConfiguration extends BaseMotanConfiguration{

    protected abstract AnnotationBean annotationBean();
    protected abstract ProtocolConfigBean protocolConfigBean();
    protected abstract RegistryConfigBean registryConfigBean();
    protected abstract BasicServiceConfigBean basicServiceConfigBean(
            RegistryConfigBean defaultRegistry,
            ProtocolConfigBean defaultProtocol);
    protected abstract BasicRefererConfigBean basicRefererConfigBean(
            RegistryConfigBean defaultRegistry,
            ProtocolConfigBean defaultProtocol);

}
