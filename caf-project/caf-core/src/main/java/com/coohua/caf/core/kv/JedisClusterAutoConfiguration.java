package com.coohua.caf.core.kv;

import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.context.annotation.Bean;
import redis.clients.jedis.JedisCluster;
import redis.clients.jedis.JedisPoolConfig;

/**
 * Created by zhangrongbin on 2018/09/29.
 */
@ConditionalOnClass({
        JedisCluster.class,
        JedisPoolConfig.class
})
public class JedisClusterAutoConfiguration {


}
