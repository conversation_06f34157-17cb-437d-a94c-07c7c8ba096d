package com.coohua.caf.core.kv;

import org.springframework.context.annotation.Import;

import java.lang.annotation.*;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2018/10/10.
 *
 * 举个栗子：
 * @EnableJedisClusterClient(namespace = "redis1")
 * 创建一个JedisClient, 其bean的名字为redis1JedisClient, 在代码里这样使用即可
 *   @Autoware
 *   private JedisClusterClient redis1JedisClusterClient;
 * ps:
 *   @EnableJedisClusterClient() 等同于 -> @EnableJedisClusterClient(namespace = "default")
 *   @Autoware
 *   private JedisClusterClient defaultJedisClusterClient;
 *
 * 配置说明:
 * - redis ip: ${app.jedis-cluster.jedis1.address}
 * - redis port: ${app.jedis-cluster.jedis1.port}
 * - redis connection pool: prefix = app.jedis-cluster.jedis1.pool - 能配置的值请参考{@link redis.clients.jedis.JedisPoolConfig}
 *   默认配置值请参考{@link BaseJedisConfiguration}
 *
 * 原理:
 *   同{@link EnableJedisClient}
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
@Repeatable(EnableJedisClusterClients.class)
@Import(JedisClusterClientRegistrar.class)
public @interface EnableJedisClusterClient {
    String namespace() default "default";
}