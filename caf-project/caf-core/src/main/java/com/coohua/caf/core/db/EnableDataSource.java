package com.coohua.caf.core.db;

import org.springframework.context.annotation.Import;

import java.lang.annotation.*;

/**
 * Created by <PERSON>hangrongbin on 2018/10/10.
 *
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
@Repeatable(EnableDataSources.class)
@Import(DataSourceRegistrar.class)
public @interface EnableDataSource {
    String namespace() default "default";
    String[] mapperPackages() default {};
}