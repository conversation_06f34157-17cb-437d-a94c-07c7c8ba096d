package com.coohua.caf.core.base;

import java.io.Serializable;

/**
 * Created by <PERSON><PERSON>ian<PERSON> on 2018/8/8.
 * comment: 修改data字段名为result，msg为message
 */
public class WebMessage<T> implements Serializable {

	private static final long serialVersionUID = 3937449233218325438L;

	public static final int CODE_SUCCESS = 0;
	public static final int CODE_FAILED = -1;
	public static final int CODE_LIMITED = -2;

	public static final String MSG_NONE = "";

	private int code = CODE_SUCCESS;
	private String message = MSG_NONE;
	private T result;

	public WebMessage() {}

	public static WebMessage<String> block() {
		return new WebMessage<>(CODE_LIMITED, "request blocked by sentinel!", "");
	}

	public static <T> WebMessage<T> build(int code, String msg, T data) {
		return new WebMessage<>(code, msg, data);
	}

	public WebMessage(int code, String message, T result) {
		this.code = code;
		this.message = message;
		this.result = result;
	}

	public int getCode() {
		return code;
	}

	public void setCode(int code) {
		this.code = code;
	}

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public T getResult() {
		return result;
	}

	public void setResult(T result) {
		this.result = result;
	}

	@Override
	public String toString() {
		return "WebResult{" + "code=" + code + ", message='" + message + '\'' + ", result=" + result + '}';
	}
}
