package com.coohua.caf.core.aliyun.cdn;

import java.lang.annotation.ElementType;
import java.lang.annotation.Repeatable;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import org.springframework.context.annotation.Import;

@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
@Repeatable(EnableAliyunCDNClients.class)

@Import(AliyunCDNClientRegistrar.class)
public @interface EnableAliyunCDNClient {

	// 需要支持多个cdnClient，虽然目前不需要，以防万一。
	String beanName();

}
