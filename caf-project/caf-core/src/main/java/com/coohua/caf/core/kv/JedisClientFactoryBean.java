package com.coohua.caf.core.kv;

import com.coohua.caf.core.util.CustomizedConfigurationPropertiesBinder;
import com.pepper.metrics.integration.jedis.health.JedisHealthTracker;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.BeanNameAware;
import org.springframework.beans.factory.FactoryBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.bind.Bindable;
import org.springframework.context.EnvironmentAware;
import org.springframework.core.env.Environment;
import org.springframework.util.Assert;
import redis.clients.jedis.JedisPoolConfig;
import redis.clients.jedis.PjedisPool;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;

/**
 * Created by zhangrongbin on 2018/10/10.
 */
public class JedisClientFactoryBean extends BaseJedisConfiguration
        implements FactoryBean<JedisClient>, EnvironmentAware, BeanNameAware {
    private Environment environment;
    private String beanName;
    private static final ScheduledExecutorService executorService = Executors.newScheduledThreadPool(1);

    @Autowired
    protected CustomizedConfigurationPropertiesBinder binder;

    @Override
    public JedisClient getObject() {
        String namespace = StringUtils.substringBefore(beanName, JedisClient.class.getSimpleName());

        String addressKey = getPreFix() + "." + namespace + ".address";
        String address = environment.getProperty(addressKey);
        Assert.isTrue(StringUtils.isNotEmpty(address), String.format("%s=%s must be not null! ", addressKey, address));

        String portKey = getPreFix() + "." + namespace + ".port";
        String port = environment.getProperty(portKey);
        Assert.isTrue(StringUtils.isNotEmpty(port) && NumberUtils.isCreatable(port), String.format("%s=%s must be not null! and must be a number!", portKey, port));

        JedisPoolConfig jedisPoolConfig = createJedisPoolConfig();
        Bindable<?> target = Bindable.of(JedisPoolConfig.class).withExistingValue(jedisPoolConfig);
        binder.bind(getPreFix() + "." + namespace + ".pool", target);

        JedisClient jedisClient = new JedisClient(namespace, jedisPoolConfig, address, Integer.parseInt(port));
        PjedisPool jedisPool = jedisClient.getJedisPool();
        JedisHealthTracker.addJedisPool(namespace, jedisPool);
        return jedisClient;
    }

    protected String getPreFix() {
        return PREFIX_APP_JEDIS;
    }

    @Override
    public Class<?> getObjectType() {
        return JedisClient.class;
    }

    @Override
    public void setEnvironment(Environment environment) {
        this.environment = environment;
    }

    @Override
    public void setBeanName(String name) {
        this.beanName = name;
    }
}
