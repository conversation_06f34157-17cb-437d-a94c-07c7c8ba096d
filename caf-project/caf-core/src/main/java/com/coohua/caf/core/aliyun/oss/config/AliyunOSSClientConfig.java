package com.coohua.caf.core.aliyun.oss.config;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * Created by hepengyuan on 2019/01/16.
 */
//<property name="maxConnections" value="100"></property>
//<property name="connectionTimeout" value="5000"></property>
//<property name="maxErrorRetry" value="3"></property>
//<property name="supportCname" value="true"></property>
//<property name="socketTimeout" value="2000"></property>
@Setter
@Getter
@ToString
public class AliyunOSSClientConfig {

	private int maxConnections = 100;

	private int connectionTimeout = 5000;

	private int maxErrorRetry = 3;

	private boolean supportCname = true;

	private int socketTimeout = 2000;

}
