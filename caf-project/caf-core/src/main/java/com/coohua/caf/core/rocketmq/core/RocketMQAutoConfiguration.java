package com.coohua.caf.core.rocketmq.core;

import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.context.annotation.Bean;

import com.coohua.caf.core.rocketmq.core.producer.RocketMQProducerAspect;
import com.coohua.caf.core.rocketmq.producer.RocketMQSimpleProducer;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2018/09/29.
 */
@ConditionalOnClass({
		RocketMQSimpleProducer.class,
		DefaultMQProducer.class
})
public class RocketMQAutoConfiguration {

	@Bean
	public RocketMQProducerAspect rocketMQProducerAspect() {
		return new RocketMQProducerAspect();
	}

}
