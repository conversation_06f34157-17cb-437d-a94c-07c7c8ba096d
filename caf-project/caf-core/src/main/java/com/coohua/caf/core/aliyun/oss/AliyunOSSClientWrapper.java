package com.coohua.caf.core.aliyun.oss;

import org.springframework.web.multipart.MultipartFile;

import com.aliyun.oss.ClientConfiguration;
import com.aliyun.oss.OSSClient;
import com.aliyun.oss.model.PutObjectResult;
import com.coohua.caf.core.aliyun.oss.config.AliyunOSSClientConfig;
import com.coohua.caf.core.aliyun.oss.config.AliyunOSSClientEndpointConfig;

public class AliyunOSSClientWrapper {

	private OSSClient ossClient;

	private String bucketName;

	public AliyunOSSClientWrapper(AliyunOSSClientEndpointConfig endpointConfig, AliyunOSSClientConfig clientConfig) {

		ClientConfiguration realConfig = new ClientConfiguration();
		realConfig.setConnectionTimeout(clientConfig.getConnectionTimeout());
		realConfig.setMaxConnections(clientConfig.getMaxConnections());
		realConfig.setMaxErrorRetry(clientConfig.getMaxErrorRetry());
		realConfig.setSocketTimeout(clientConfig.getSocketTimeout());
		realConfig.setSupportCname(clientConfig.isSupportCname());
		this.bucketName = endpointConfig.getBucketName();

		this.ossClient = new OSSClient(endpointConfig.getEndpoint(), endpointConfig.getAccessKeyId(),
				endpointConfig.getSecretAccessKey(), realConfig);
	}

	public PutObjectResult putObject(String bucketName, String key, MultipartFile file, String remoteCallerIP)
			throws Exception {
		// TODO: 打点
		if (bucketName == null || key == null || file == null) {
			// 参数错误直接记录failed.
			AliyunOSSPutObjectCounter.incFailedCount(OSSErrorCode.ERRORCODE_OSS_PUTOBJECT_REQUEST_PARAMS_INVALID,
					remoteCallerIP, -1, -1);
			return null;
		}

		String fileContentType = null;
		PutObjectResult result = null;
		long fileSize = 0;
		long beginTS = System.currentTimeMillis();
		long endTS = beginTS;
		try {
			fileContentType = file.getContentType();
			fileSize = file.getSize();
			result = ossClient.putObject(bucketName, key, file.getInputStream());
			endTS = System.currentTimeMillis();
			return processPutObjectResult(result, fileContentType, fileSize, endTS - beginTS, remoteCallerIP);
		} catch (Exception e) {
			endTS = System.currentTimeMillis();
			processPutObjectResult(result, e, fileContentType, fileSize, endTS - beginTS, remoteCallerIP);
			throw e;
		}
	}

	private PutObjectResult processPutObjectResult(PutObjectResult result, String fileContentType, long fileSize,
			long timeCost, String remoteCallerIP) {
		return this.processPutObjectResult(result, null, fileContentType, fileSize, timeCost, remoteCallerIP);
	}

	private PutObjectResult processPutObjectResult(PutObjectResult result, Exception e, String fileContentType,
			long fileSize, long timeCost, String remoteCallerIP) {

		if (e == null && result != null && result.getRequestId() != null) {
			// SUCCESS判断标准：没有异常抛出 && requestID!=null 作为判断标准；
			// 成功
			AliyunOSSPutObjectCounter.incSuccessCount(remoteCallerIP, fileSize, timeCost);
		} else {
			if (result == null) {
				// 说明调用aliyunOSSClientSDK返回NULL.
				AliyunOSSPutObjectCounter.incFailedCount(OSSErrorCode.ERRORCODE_OSS_PUTOBJECT_RETURN_NULL,
						remoteCallerIP, fileSize, timeCost);
			} else if (result.getRequestId() == null) {
				// requestId为Null.
				AliyunOSSPutObjectCounter.incFailedCount(OSSErrorCode.ERRORCODE_OSS_PUTOBJECT_RETURN_REQUESTID_NULL,
						remoteCallerIP, fileSize, timeCost);
			} else if (e != null) {
				// exception
				AliyunOSSPutObjectCounter.incFailedCount(OSSErrorCode.ERRORCODE_OSS_PUTOBJECT_EXCEPTION, e,
						remoteCallerIP, fileSize, timeCost);
			} else {
				// other happping
				AliyunOSSPutObjectCounter.incFailedCount(OSSErrorCode.ERRORCODE_OSS_PUTOBJECT_OTHER, remoteCallerIP,
						fileSize, timeCost);
			}
		}
		return result;
	}

	public String getBucketName() {
		return bucketName;
	}

}
