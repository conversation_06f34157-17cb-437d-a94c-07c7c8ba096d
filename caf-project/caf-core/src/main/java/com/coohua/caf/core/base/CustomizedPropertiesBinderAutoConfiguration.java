package com.coohua.caf.core.base;

import com.coohua.caf.core.util.CustomizedConfigurationPropertiesBinder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;

/**
 * Created by zhangrongbin on 2018/09/28.
 */
@Order(1)
public class CustomizedPropertiesBinderAutoConfiguration {
    @Bean
    public CustomizedConfigurationPropertiesBinder customizedConfigurationPropertiesBinder() {
        return new CustomizedConfigurationPropertiesBinder();
    }
}
