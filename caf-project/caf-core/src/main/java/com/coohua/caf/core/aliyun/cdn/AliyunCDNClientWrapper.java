package com.coohua.caf.core.aliyun.cdn;

import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.cdn.model.v20180510.PushObjectCacheRequest;
import com.aliyuncs.cdn.model.v20180510.PushObjectCacheResponse;
import com.aliyuncs.profile.DefaultProfile;
import com.coohua.caf.core.aliyun.cdn.config.AliyunCDNClientConfig;
import com.coohua.caf.core.aliyun.cdn.dto.PushObjectCacheResponseDTO;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class AliyunCDNClientWrapper {

	private AliyunCDNClientConfig cdnClientConfig;

	public AliyunCDNClientWrapper(AliyunCDNClientConfig cdnClientConfig) {
		this.cdnClientConfig = cdnClientConfig;
	}

	public PushObjectCacheResponseDTO pushObjectCache(String cdnDomain, String key) {
		PushObjectCacheResponseDTO dto = new PushObjectCacheResponseDTO();
		dto.setEnableCDNPreload(cdnClientConfig.isEnableCDNPreload());

		// 没有开启cdn preload就直接返回
		if (!dto.isEnableCDNPreload()) {
			return dto;
		}

		// 创建DefaultAcsClient实例并初始化
		DefaultProfile profile = DefaultProfile.getProfile("cn-hangzhou", cdnClientConfig.getAccessKeyId(),
				cdnClientConfig.getSecretAccessKey());
		DefaultAcsClient client = new DefaultAcsClient(profile);
		PushObjectCacheRequest request = new PushObjectCacheRequest();
		request.setActionName("PushObjectCache");
		// 加速的文件位置，wdtest.licai.cn为配置的域名，如wdtest.licai.cn/helloworld.jpg
		StringBuilder objectPath = new StringBuilder();
		objectPath.append(cdnDomain);
		if (key.startsWith("/")) {
			objectPath.append(key);
		} else {
			objectPath.append("/").append(key);
		}
		request.setObjectPath(objectPath.toString());
		// 发起请求并处理应答或异常
		PushObjectCacheResponse response = null;
		try {
			response = client.getAcsResponse(request);
			if (response != null) {
				dto.setPreloadPushTaskId(response.getPushTaskId());
				dto.setPreloadRequestId(response.getRequestId());
			}
			return dto;
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}

		if (response != null) {
			dto.setPreloadPushTaskId(response.getPushTaskId());
			dto.setPreloadRequestId(response.getRequestId());
			return dto;
		} else {
			return dto;
		}
	}
}
