package com.coohua.caf.core.elasticsearch;

import java.util.HashMap;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.support.BeanDefinitionRegistry;
import org.springframework.context.annotation.ImportBeanDefinitionRegistrar;
import org.springframework.core.annotation.AnnotationAttributes;
import org.springframework.core.type.AnnotationMetadata;
import org.springframework.util.Assert;

import com.coohua.caf.core.util.BeanRegistrationUtil;

/**
 * Created by hepengyuan on 2018/01/16.
 */
public class ElasticsearchTemplateRegistrar implements ImportBeanDefinitionRegistrar {

	@Override
	public void registerBeanDefinitions(AnnotationMetadata importingClassMetadata, BeanDefinitionRegistry registry) {
		boolean processed = false;
		{
			AnnotationAttributes attributes = AnnotationAttributes.fromMap(
					importingClassMetadata.getAnnotationAttributes(EnableElasticsearchTemplate.class.getName()));
			if (attributes != null) {
				dealOne(registry, attributes);
				processed = true;
			}
		}
		{
			AnnotationAttributes attributes = AnnotationAttributes.fromMap(
					importingClassMetadata.getAnnotationAttributes(EnableElasticsearcheTemplates.class.getName()));
			if (attributes != null) {
				AnnotationAttributes[] annotationArray = attributes.getAnnotationArray("value");
				if (annotationArray != null && annotationArray.length > 0) {
					for (AnnotationAttributes oneAttributes : annotationArray) {
						dealOne(registry, oneAttributes);
						processed = true;
					}
				}
			}
		}
		if (!processed)
			throw new IllegalStateException("no @EnableRocketConsumer or @EnableRocketConsumers found! pls check!");
	}

	protected static Map<String, String> templateBeanNameToClusterNameMap = new HashMap<String, String>();

	private void dealOne(BeanDefinitionRegistry registry, AnnotationAttributes oneAttributes) {
		String namespace = oneAttributes.getString("namespace");
		String cluster = oneAttributes.getString("cluster");
		boolean enableMultiElasticsearchSource = oneAttributes.getBoolean("enableMultiElasticsearchSource");

		Assert.isTrue(StringUtils.isNotEmpty(namespace), "namespace must be specified !");
		Assert.isTrue(!namespace.contains(":"), "namespace couldn't contain char ':' !");

		// Assert.isTrue(cluster != null, "cluster couldn't be null !");

		String beanName = namespace;
		if (enableMultiElasticsearchSource && !StringUtils.isEmpty(cluster) && cluster.trim().length() != 0) {
			beanName = new StringBuilder().append(namespace).append(":").append(cluster).toString();
		}

		templateBeanNameToClusterNameMap.put(beanName, cluster);

		BeanRegistrationUtil.registerBeanDefinitionIfBeanNameNotExists(registry, beanName,
				ElasticsearchTemplateFactory.class);
	}

}
