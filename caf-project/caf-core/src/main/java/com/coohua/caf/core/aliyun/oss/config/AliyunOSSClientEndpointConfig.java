package com.coohua.caf.core.aliyun.oss.config;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * Created by hepen<PERSON><PERSON> on 2019/01/16.
 */
//<constructor-arg type="java.lang.String" value="#{app['oss.url']}"/>
//<constructor-arg type="java.lang.String" value="#{app['oss.id']}"/>
//<constructor-arg type="java.lang.String" value="#{app['oss.secret']}"/>
//<constructor-arg type="com.aliyun.oss.ClientConfiguration" ref="clientConfiguration"/>	
@Setter
@Getter
@ToString
public class AliyunOSSClientEndpointConfig {

	private String endpoint;

	private String bucketName;

	private String accessKeyId;

	private String secretAccessKey;

}
