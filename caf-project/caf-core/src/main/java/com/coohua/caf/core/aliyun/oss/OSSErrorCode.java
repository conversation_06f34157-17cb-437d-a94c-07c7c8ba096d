package com.coohua.caf.core.aliyun.oss;

import lombok.Getter;

@Getter
public class OSSErrorCode {

	private String code;

	private String desc;

	private OSSErrorCode(String code, String desc) {
		this.code = code;
		this.desc = desc;
	}

	// 传入ossClient参数错误直接记录failed.
	static String CODE_OSS_PUTOBJECT_SUCCESS = "CODE_OSS_PUTOBJECT_SUCCESS";
	static OSSErrorCode ERRORCODE_OSS_PUTOBJECT_SUCCESS = new OSSErrorCode(CODE_OSS_PUTOBJECT_SUCCESS,
			"invalid params.");

	// 传入ossClient参数错误直接记录failed.
	static String CODE_OSS_PUTOBJECT_REQUEST_PARAMS_INVALID = "CODE_OSS_PUTOBJECT_REQUEST_PARAMS_INVALID";
	static OSSErrorCode ERRORCODE_OSS_PUTOBJECT_REQUEST_PARAMS_INVALID = new OSSErrorCode(
			CODE_OSS_PUTOBJECT_REQUEST_PARAMS_INVALID, "传入aliyun的ossClient的参数非法.");

	// 说明调用aliyunOSSClientSDK返回NULL.
	static String CODE_OSS_PUTOBJECT_RETURN_NULL = "CODE_OSS_PUTOBJECT_RETURN_NULL";
	static OSSErrorCode ERRORCODE_OSS_PUTOBJECT_RETURN_NULL = new OSSErrorCode(CODE_OSS_PUTOBJECT_RETURN_NULL,
			"result is null.");

	// 说明调用aliyunOSSClientSDK返回的PutObjectResult不为NULL,但是他的属性Response为NULL.
	static String CODE_OSS_PUTOBJECT_RETURN_REQUESTID_NULL = "CODE_OSS_PUTOBJECT_RETURN_REQUESTID_NULL";
	static OSSErrorCode ERRORCODE_OSS_PUTOBJECT_RETURN_REQUESTID_NULL = new OSSErrorCode(
			CODE_OSS_PUTOBJECT_RETURN_REQUESTID_NULL, "requestId is null.");

	// exception
	static String CODE_OSS_PUTOBJECT_EXCEPTION = "CODE_OSS_PUTOBJECT_EXCEPTION";
	static OSSErrorCode ERRORCODE_OSS_PUTOBJECT_EXCEPTION = new OSSErrorCode(CODE_OSS_PUTOBJECT_EXCEPTION,
			"exception.");

	// other happening
	static String CODE_OSS_PUTOBJECT_OTHER = "CODE_OSS_PUTOBJECT_OTHER";
	static OSSErrorCode ERRORCODE_OSS_PUTOBJECT_OTHER = new OSSErrorCode(CODE_OSS_PUTOBJECT_OTHER, "other");

}
