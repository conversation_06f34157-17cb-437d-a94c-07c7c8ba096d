package com.coohua.caf.core.rocketmq.core.producer;

import com.coohua.caf.core.rocketmq.core.RocketMQBaseFactory;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2018/12/12.
 */
public class RocketMQBaseProducerFactory extends RocketMQBaseFactory {

	public static final String PREFIX_APP_ROCKET_PRODUCER = "caf.mq.rocketmq.producer";

	protected RocketMQProducerConfig createRocketProducerConfig() {
		RocketMQProducerConfig config = new RocketMQProducerConfig();
		return config;
	}

	protected String getRocketProducerPrefix() {
		return PREFIX_APP_ROCKET_PRODUCER;
	}

}
