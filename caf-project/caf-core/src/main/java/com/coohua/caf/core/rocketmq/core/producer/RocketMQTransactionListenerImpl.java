package com.coohua.caf.core.rocketmq.core.producer;

import java.util.concurrent.atomic.AtomicInteger;

import org.apache.rocketmq.client.producer.LocalTransactionState;
import org.apache.rocketmq.client.producer.TransactionListener;
import org.apache.rocketmq.common.message.Message;
import org.apache.rocketmq.common.message.MessageExt;
import org.jboss.netty.util.internal.ConcurrentHashMap;

/**
 * 
 * <AUTHOR>
 * @version create time: 2018年10月25日 上午11:04:18
 *
 */
public class RocketMQTransactionListenerImpl implements TransactionListener {
	private AtomicInteger transactionIndex = new AtomicInteger(0);

	private ConcurrentHashMap<String, Integer> localTrans = new ConcurrentHashMap<>();

	@Override
	public LocalTransactionState executeLocalTransaction(Message msg, Object arg) {
		int value = transactionIndex.getAndIncrement();
		int status = value % 3;
		localTrans.put(msg.getTransactionId(), status);
		return LocalTransactionState.UNKNOW;
	}

	@Override
	public LocalTransactionState checkLocalTransaction(MessageExt msg) {
		Integer status = localTrans.get(msg.getTransactionId());
		if (null != status) {
			switch (status) {
			case 0:
				return LocalTransactionState.UNKNOW;
			case 1:
				return LocalTransactionState.COMMIT_MESSAGE;
			case 2:
				return LocalTransactionState.ROLLBACK_MESSAGE;
			}
		}
		return LocalTransactionState.COMMIT_MESSAGE;
	}
}
