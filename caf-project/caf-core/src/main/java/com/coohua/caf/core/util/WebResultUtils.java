package com.coohua.caf.core.util;

import com.coohua.caf.core.base.WebResult;

/**
 * Created by <PERSON><PERSON> <PERSON>ian<PERSON> on 2018/8/8.
 */
public class WebResultUtils {

    public static <T> WebResult<T> newResult(int code, String msg, T data) {
        return new WebResult<T>(code, msg, data);
    }

    public static <T> WebResult<T> success(T data) {
        return new WebResult<T>(WebResult.CODE_SUCCESS, WebResult.MSG_NONE, data);
    }

    public static <T> WebResult<T> error(int code, String msg) {
        return new WebResult<T>(code, msg, null);
    }
}
