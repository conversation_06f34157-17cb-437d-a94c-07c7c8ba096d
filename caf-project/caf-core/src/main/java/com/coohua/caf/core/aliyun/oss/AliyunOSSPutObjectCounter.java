package com.coohua.caf.core.aliyun.oss;

import io.prometheus.client.Counter;

public class AliyunOSSPutObjectCounter {

	// counter: "rtCode", "exception", "remoteCallerIP"
	// no monitor: "fileContentType"

	private static Counter PUTOBJECT_COUNTER;

	private static volatile boolean hasInit = false;

	static void init() {
		if (!hasInit) {
			synchronized (AliyunOSSPutObjectHistogram.class) {
				if (!hasInit) {
					PUTOBJECT_COUNTER = Counter.build().name("aliyun_oss_putobject_counter")
							.help("the count of putobject method.").labelNames("rtCode", "exception", "remoteCallerIP")
							.register();
					hasInit = true;
				}
			}
		}
	}

	static void incFailedCount(OSSErrorCode ossErrorCode, Exception exception, String remoteCallerIP, long fileSize,
			long timeCost) {
		String desc = ossErrorCode.getCode();
		if (exception != null) {
			desc = exception.getMessage();
		}
		PUTOBJECT_COUNTER.labels(ossErrorCode.getCode(), desc, remoteCallerIP).inc();
		AliyunOSSPutObjectHistogram.observeFileSize(ossErrorCode, exception, remoteCallerIP, fileSize);
		AliyunOSSPutObjectHistogram.observeTimeCost(ossErrorCode, exception, remoteCallerIP, timeCost);
	}

	static void incFailedCount(OSSErrorCode ossErrorCode, String remoteCallerIP, long fileSize, long timeCost) {
		incFailedCount(ossErrorCode, null, remoteCallerIP, fileSize, timeCost);
	}

	static void incSuccessCount(String remoteCallerIP, long fileSize, long timeCost) {
		PUTOBJECT_COUNTER.labels(OSSErrorCode.CODE_OSS_PUTOBJECT_SUCCESS, OSSErrorCode.CODE_OSS_PUTOBJECT_SUCCESS,
				remoteCallerIP).inc();
		AliyunOSSPutObjectHistogram.observeFileSize(OSSErrorCode.ERRORCODE_OSS_PUTOBJECT_SUCCESS, remoteCallerIP,
				fileSize);
		AliyunOSSPutObjectHistogram.observeTimeCost(OSSErrorCode.ERRORCODE_OSS_PUTOBJECT_SUCCESS, remoteCallerIP,
				timeCost);
	}

	static void clear() {
		PUTOBJECT_COUNTER.clear();
	}

}
