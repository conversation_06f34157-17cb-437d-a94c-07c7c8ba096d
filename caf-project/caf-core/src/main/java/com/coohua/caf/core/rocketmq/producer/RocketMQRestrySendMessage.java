package com.coohua.caf.core.rocketmq.producer;

import org.apache.rocketmq.client.producer.MQProducer;
import org.apache.rocketmq.common.message.Message;

import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class RocketMQRestrySendMessage {

	private MQProducer mqProducer;

	private Message message;

	private String topic;

	private String producerGroup;

	private String clazzName;

	private int retryCount = 1;

	private long firstRetrySendTimeStamp;

	private long lastRetrySendTimeStamp;

	public RocketMQRestrySendMessage(MQProducer mqProducer, Message message, String producerGroup, String clazzName) {
		this.mqProducer = mqProducer;
		this.message = message;
		this.firstRetrySendTimeStamp = System.currentTimeMillis();
		this.lastRetrySendTimeStamp = this.firstRetrySendTimeStamp;

		this.topic = message.getTopic();
		this.producerGroup = producerGroup;
		this.clazzName = clazzName;
	}

}
