package com.coohua.caf.core.kv;

import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;

/**
 * Created by z<PERSON>rongbin on 2018/09/29.
 */
@ConditionalOnClass({
        Jedis.class,
        JedisPool.class,
        JedisPoolConfig.class
})
public class JedisAutoConfiguration {


}
