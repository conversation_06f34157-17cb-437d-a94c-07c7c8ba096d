package com.coohua.caf.core.aliyun.oss;

import io.prometheus.client.Histogram;

public class AliyunOSSPutObjectHistogram {

	//// histogram: "timeCost", "fileSize"

//	private static Histogram PUTOBJECT_TIME_COUST_HISTOGRAM = Histogram.build()
//			.name("aliyun_oss_putobject_time_cost_histogram").help("put file to oss's time cost stat.")
//			.labelNames("rtCode", "exception", "remoteCallerIP").buckets(0, 10, 100, 1000, 1000 * 5, 1000 * 10,
//					1000 * 30, 1000 * 60, 1000 * 60 * 10, 1000 * 60 * 30, 1000 * 60 * 60)
//			.register();
//
//	private static Histogram PUTOBJECT_FILE_SIZE_HISTOGRAM = Histogram.build()
//			.name("aliyun_oss_putobject_time_cost_histogram").help("put file to oss's file size stat.")
//			.labelNames("rtCode", "exception", "remoteCallerIP")
//			.buckets(0, 1024, 1024 * 10, 1024 * 20, 1024 * 30, 1024 * 40, 1024 * 50, 1024 * 100, 1024 * 1024,
//					1024 * 1024 * 10, 1024 * 1024 * 100, 1024 * 1024 * 1024)
//			.register();

	private static Histogram PUTOBJECT_TIME_COUST_HISTOGRAM;

	private static Histogram PUTOBJECT_FILE_SIZE_HISTOGRAM;

	private static volatile boolean hasInit = false;

	static void init() {
		if (!hasInit) {
			synchronized (AliyunOSSPutObjectHistogram.class) {
				if (!hasInit) {
					PUTOBJECT_FILE_SIZE_HISTOGRAM = Histogram.build().name("aliyun_oss_putobject_file_size_histogram")
							.help("put file to oss's file size stat.")
							.labelNames("rtCode", "exception", "remoteCallerIP")
							.buckets(0, 1024, 1024 * 10, 1024 * 20, 1024 * 30, 1024 * 40, 1024 * 50, 1024 * 100,
									1024 * 1024, 1024 * 1024 * 10, 1024 * 1024 * 100, 1024 * 1024 * 1024)
							.register();

					PUTOBJECT_TIME_COUST_HISTOGRAM = Histogram.build().name("aliyun_oss_putobject_time_cost_histogram")
							.help("put file to oss's time cost stat.")
							.labelNames("rtCode", "exception", "remoteCallerIP").buckets(0, 10, 100, 1000, 1000 * 5,
									1000 * 10, 1000 * 30, 1000 * 60, 1000 * 60 * 10, 1000 * 60 * 30, 1000 * 60 * 60)
							.register();
					hasInit = true;
				}
			}

		}
	}

	static void observeTimeCost(OSSErrorCode ossErrorCode, String remoteCallerIP, long timeCost) {
		observeTimeCost(ossErrorCode, null, remoteCallerIP, timeCost);
	}

	static void observeTimeCost(OSSErrorCode ossErrorCode, Exception exception, String remoteCallerIP, long timeCost) {
		String desc = ossErrorCode.getCode();
		if (exception != null) {
			desc = exception.getMessage();
		}
		PUTOBJECT_TIME_COUST_HISTOGRAM.labels(ossErrorCode.getCode(), desc, remoteCallerIP).observe(timeCost);
	}

	static void observeFileSize(OSSErrorCode ossErrorCode, String remoteCallerIP, long fileSize) {
		observeFileSize(ossErrorCode, null, remoteCallerIP, fileSize);
	}

	static void observeFileSize(OSSErrorCode ossErrorCode, Exception exception, String remoteCallerIP, long fileSize) {
		String desc = ossErrorCode.getCode();
		if (exception != null) {
			desc = exception.getMessage();
		}
		PUTOBJECT_FILE_SIZE_HISTOGRAM.labels(ossErrorCode.getCode(), desc, remoteCallerIP).observe(fileSize);
	}
}
