package com.coohua.caf.core.aliyun.oss;

import java.lang.annotation.ElementType;
import java.lang.annotation.Repeatable;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import org.springframework.context.annotation.Import;

@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
@Repeatable(EnableAliyunOSSClients.class)

@Import(AliyunOSSClientRegistrar.class)
public @interface EnableAliyunOSSClient {

	// 需要支持多个ossClient/bucket
	String beanName();

}
