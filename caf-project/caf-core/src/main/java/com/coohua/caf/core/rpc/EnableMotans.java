package com.coohua.caf.core.rpc;

import org.springframework.context.annotation.Import;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
/**
 * Created by zhangrongbin on 2018/9/26.
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
@Import(MotanRegistrar.class)
public @interface EnableMotans {
    EnableMotan[] value();
}