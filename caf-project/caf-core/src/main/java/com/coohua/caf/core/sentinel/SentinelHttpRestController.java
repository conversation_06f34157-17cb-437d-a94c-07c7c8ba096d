package com.coohua.caf.core.sentinel;

import com.alibaba.csp.sentinel.slots.block.BlockException;
import com.coohua.caf.core.base.WebResult;
import com.coohua.caf.core.logging.Loggers;
import com.coohua.caf.core.util.HttpUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * Created by zhangrongbin on 2019/5/10.
 */
@Slf4j
@ControllerAdvice
public class SentinelHttpRestController {

    @ExceptionHandler({BlockException.class})
    @ResponseBody
    public WebResult handle(HttpServletRequest request, HttpServletResponse response, BlockException ex) {
//    public WebResult handle(HttpServletRequest request, HttpServletResponse response) {
        String metrics = request.getMethod() + " " + HttpUtil.getPatternUrl(request.getRequestURI());
        Loggers.getAccessLogger().warn("http request blocked! -> {}", metrics);
        return WebResult.block();
    }

}