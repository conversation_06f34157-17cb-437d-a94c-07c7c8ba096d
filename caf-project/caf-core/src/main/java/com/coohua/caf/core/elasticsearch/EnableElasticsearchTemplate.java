package com.coohua.caf.core.elasticsearch;

import java.lang.annotation.ElementType;
import java.lang.annotation.Repeatable;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import org.springframework.context.annotation.Import;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2018/12/12.
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
@Repeatable(EnableElasticsearcheTemplates.class)

@Import(ElasticsearchTemplateRegistrar.class)
public @interface EnableElasticsearchTemplate {

	String namespace() default "elasticsearchTemplate";

	// 需要支持多个es集群
	String cluster();

	boolean enableMultiElasticsearchSource() default false;

}