package com.coohua.caf.core.elasticsearch;

import java.net.InetAddress;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.client.transport.TransportClient;
import org.elasticsearch.common.settings.Settings;
import org.elasticsearch.common.settings.Settings.Builder;
import org.elasticsearch.common.transport.TransportAddress;
import org.elasticsearch.common.unit.ByteSizeUnit;
import org.elasticsearch.transport.client.PreBuiltTransportClient;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.BeanNameAware;
import org.springframework.beans.factory.FactoryBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.bind.Bindable;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.EnvironmentAware;
import org.springframework.core.env.Environment;
import org.springframework.data.elasticsearch.core.ElasticsearchTemplate;

import com.coohua.caf.core.util.CustomizedConfigurationPropertiesBinder;
import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.ConfigService;

import lombok.extern.slf4j.Slf4j;

/**
 * Created by hepengyuan on 2018/12/12.
 */
@Slf4j
public class ElasticsearchTemplateFactory
		implements FactoryBean<ElasticsearchTemplate>, EnvironmentAware, BeanNameAware, ApplicationContextAware {
	private Environment environment;
	private String beanName;
	private ApplicationContext applicationContext;

	@Autowired
	protected CustomizedConfigurationPropertiesBinder binder;

	private String CONFIG_PREFIX = "caf.es";

	@Override
	public ElasticsearchTemplate getObject() throws Exception {

		ElasticsearchClientConfig esClientConfig = new ElasticsearchClientConfig();
		Bindable<?> target = Bindable.of(ElasticsearchClientConfig.class).withExistingValue(esClientConfig);

		// 需要支持多个es集群
		String clusterName = ElasticsearchTemplateRegistrar.templateBeanNameToClusterNameMap.get(beanName);
		binder.bind(CONFIG_PREFIX + "." + clusterName, target);

		// 获取apollo中的配置
		String esServersStr = esClientConfig.getElasticsearchServers();
		if ("null".equalsIgnoreCase(esServersStr) || StringUtils.isEmpty(esServersStr)) {
			return null;
		}

		String[] esServers = esServersStr.split(",");

//		Settings settings = Settings.builder().put("cluster.name", esClientConfig.getElasticsearchClusterName())
//				.build();
		// TransportClient client = new PreBuiltTransportClient(settings);
		// TransportClient client = new PreBuiltTransportClient(Settings.EMPTY);

		TransportClient client = new PreBuiltTransportClient(loadElasticsearchClientConfig(clusterName));

		for (String server : esServers) {
			String[] hostAndPort = server.split(":");
			client.addTransportAddress(
					new TransportAddress(InetAddress.getByName(hostAndPort[0]), Integer.parseInt(hostAndPort[1])));
		}

		ElasticsearchTemplate template = new ElasticsearchTemplate(client);
		return template;
	}

	@Override
	public Class<?> getObjectType() {
		return ElasticsearchTemplateFactory.class;
	}

	@Override
	public void setEnvironment(Environment environment) {
		this.environment = environment;
	}

	@Override
	public void setBeanName(String name) {
		this.beanName = name;
	}

	@Override
	public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
		this.applicationContext = applicationContext;
	}

	public Settings loadElasticsearchClientConfig(String clusterName) {
		Config apolloConfig = ConfigService.getConfig(CONFIG_PREFIX + "." + clusterName + ".clientConfig");
		Set<String> propertyNames = apolloConfig.getPropertyNames();

		Builder builder = Settings.builder();
		for (String key : propertyNames) {
			String originValue = apolloConfig.getProperty(key, null);

			if (originValue == null) {
				log.error(new StringBuilder("apollo config key:").append(key)
						.append(" is configed invalied, originValue is ").append(originValue).toString());
				continue;
			}

			String[] array = originValue.split(";");
			int len = array.length;

			// array的合法长度只有2,3.
			// 1;time;timeunit
			// 1024;bytesize;bytesizeunit
			// 1;byte/short/int/long/float/double/boolean/char
			if (len == 2) {
				loadValue(builder, key, array[0], array[1], null, originValue);
			} else if (len == 3) {
				loadValue(builder, key, array[0], array[1], array[2], originValue);
			} else {
				log.error(new StringBuilder("apollo config key:").append(key)
						.append(" is configed invalied, size invalid. originValue is ").append(originValue).toString());
				continue;
			}
		}
		return builder.build();
	}

	public void loadValue(Builder builder, String key, String value, String valueType, String valueUnit,
			String originValue) {
		try {
			if ("time".equalsIgnoreCase(valueType)) {
				if (TimeUnit.DAYS.toString().equalsIgnoreCase(valueUnit)) {
					builder.put(key, Long.parseLong(value), TimeUnit.DAYS);
				} else if (TimeUnit.HOURS.toString().equalsIgnoreCase(valueUnit)) {
					builder.put(key, Long.parseLong(value), TimeUnit.HOURS);
				} else if (TimeUnit.MICROSECONDS.toString().equalsIgnoreCase(valueUnit)) {
					builder.put(key, Long.parseLong(value), TimeUnit.MICROSECONDS);
				} else if (TimeUnit.MILLISECONDS.toString().equalsIgnoreCase(valueUnit)) {
					builder.put(key, Long.parseLong(value), TimeUnit.MILLISECONDS);
				} else if (TimeUnit.MINUTES.toString().equalsIgnoreCase(valueUnit)) {
					builder.put(key, Long.parseLong(value), TimeUnit.MINUTES);
				} else if (TimeUnit.NANOSECONDS.toString().equalsIgnoreCase(valueUnit)) {
					builder.put(key, Long.parseLong(value), TimeUnit.NANOSECONDS);
				} else if (TimeUnit.SECONDS.toString().equalsIgnoreCase(valueUnit)) {
					builder.put(key, Long.parseLong(value), TimeUnit.SECONDS);
				} else {
					log.error(new StringBuilder("apollo config key:").append(key)
							.append(" is configed invalied, originValue is ").append(originValue).toString());
				}
			} else if ("bytesize".equalsIgnoreCase(valueType)) {
				if (ByteSizeUnit.BYTES.toString().equalsIgnoreCase(valueUnit)) {
					builder.put(key, Long.parseLong(value), ByteSizeUnit.BYTES);
				} else if (ByteSizeUnit.BYTES.toString().equalsIgnoreCase(valueUnit)) {
					builder.put(key, Long.parseLong(value), ByteSizeUnit.BYTES);
				} else if (ByteSizeUnit.GB.toString().equalsIgnoreCase(valueUnit)) {
					builder.put(key, Long.parseLong(value), ByteSizeUnit.GB);
				} else if (ByteSizeUnit.KB.toString().equalsIgnoreCase(valueUnit)) {
					builder.put(key, Long.parseLong(value), ByteSizeUnit.KB);
				} else if (ByteSizeUnit.MB.toString().equalsIgnoreCase(valueUnit)) {
					builder.put(key, Long.parseLong(value), ByteSizeUnit.MB);
				} else if (ByteSizeUnit.PB.toString().equalsIgnoreCase(valueUnit)) {
					builder.put(key, Long.parseLong(value), ByteSizeUnit.PB);
				} else if (ByteSizeUnit.TB.toString().equalsIgnoreCase(valueUnit)) {
					builder.put(key, Long.parseLong(value), ByteSizeUnit.TB);
				} else {
					log.error(new StringBuilder("apollo config key:").append(key)
							.append(" is configed invalied, originValue is ").append(originValue).toString());
				}
			}
			// byte/short/int/long/float/double/boolean/char
			else if ("int".equalsIgnoreCase(valueType) || "integer".equalsIgnoreCase(valueType)) {
				builder.put(key, Integer.parseInt(value));
			} else if ("long".equalsIgnoreCase(valueType)) {
				builder.put(key, Long.parseLong(value));
			} else if ("float".equalsIgnoreCase(valueType)) {
				builder.put(key, Float.parseFloat(value));
			} else if ("double".equalsIgnoreCase(valueType)) {
				builder.put(key, Double.parseDouble(value));
			} else if ("boolean".equalsIgnoreCase(valueType) || "bool".equalsIgnoreCase(valueType)) {
				builder.put(key, Boolean.parseBoolean(value));
			} else if ("string".equalsIgnoreCase(valueType)) {
				builder.put(key, value);
			} else {
				log.error(new StringBuilder("apollo config key:").append(key)
						.append(" is configed invalied, originValue is ").append(originValue).toString());
			}
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
	}
}
