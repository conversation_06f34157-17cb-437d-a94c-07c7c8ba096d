package com.coohua.caf.core.base;

import com.coohua.caf.core.metrics.CustomProfilerAspect;
import com.coohua.caf.core.metrics.MonitorConfig;
import org.springframework.boot.actuate.health.Health;
import org.springframework.boot.actuate.health.HealthIndicator;
import org.springframework.context.annotation.Bean;

/**
 * Created by <PERSON>hangrongbin on 2018/11/12.
 */
public class FrameworkConfigAutoConfiguration {

    @Bean
    public MonitorConfig monitorConfig() {
        return new MonitorConfig();
    }

    @Bean
    public CustomProfilerAspect customProfilerAspect() {
        return new CustomProfilerAspect();
    }

    @Bean
    public WarmUpRegistry warmUpRegistry() {
        return new WarmUpRegistry();
    }

    @Bean
    public HealthIndicator warmUpIndicator() {
        return () -> {
            if (WarmUpResult.isDone()) {
                return Health.up().build();
            } else {
                return Health.down().withDetail("warmUp", "NO").build();
            }
        };
    }
}
