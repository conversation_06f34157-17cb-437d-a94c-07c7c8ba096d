package com.coohua.caf.core.rocketmq.core.producer;

import java.lang.annotation.ElementType;
import java.lang.annotation.Repeatable;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import org.springframework.context.annotation.Import;

/**
 * Created by he<PERSON>gyuan on 2018/12/12.
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
@Repeatable(EnableRocketMQProducers.class)
@Import(RocketMQProducerRegistrar.class)
public @interface EnableRocketMQProducer {

	String namespace() default "default";

	String producerGroup() default "";

	boolean autoStart() default false;

}