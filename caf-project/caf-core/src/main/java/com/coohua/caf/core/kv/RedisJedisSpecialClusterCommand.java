package com.coohua.caf.core.kv;

import redis.clients.jedis.Tuple;

import java.util.Set;
/**
 * Created by z<PERSON><PERSON><PERSON> on 2018/11/20.
 */
public interface RedisJedisSpecialClusterCommand {
    Set<Tuple> zrangeByScoreWithScores(String key, double min, double max);

    Set<Tuple> zrevrangeByScoreWithScores(String key, double max, double min);

    Set<Tuple> zrangeByScoreWithScores(String key, double min, double max, int offset, int count);

    Set<Tuple> zrangeByScoreWithScores(String key, String min, String max);

    Set<Tuple> zrevrangeByScoreWithScores(String key, String max, String min);

    Set<Tuple> zrangeByScoreWithScores(String key, String min, String max, int offset, int count);

    Set<Tuple> zrevrangeByScoreWithScores(String key, double max, double min, int offset, int count);

    Set<Tuple> zrevrangeByScoreWithScores(String key, String max, String min, int offset, int count);

    Set<Tuple> zrangeWithScores(String key, long start, long end);
}
