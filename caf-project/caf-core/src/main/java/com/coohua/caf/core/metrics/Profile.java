package com.coohua.caf.core.metrics;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Created by <PERSON>hangrongbin on 2018/11/27.
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({
    ElementType.METHOD
})
public @interface Profile {
    ProfileType type() default ProfileType.LATENCY;
}
