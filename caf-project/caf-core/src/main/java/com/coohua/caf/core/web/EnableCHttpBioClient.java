package com.coohua.caf.core.web;

import org.springframework.context.annotation.Import;

import java.lang.annotation.*;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2019/4/26.
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
@Repeatable(EnableCHttpBioClients.class)
@Import(CHttpBioClientRegistrar.class)
public @interface EnableCHttpBioClient {
    String namespace() default "default";
}