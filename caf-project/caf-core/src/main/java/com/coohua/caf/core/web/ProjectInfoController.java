package com.coohua.caf.core.web;

import com.coohua.caf.core.base.WebResult;
import com.google.common.collect.Maps;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2019/03/13.
 */
@RestController
@RequestMapping(value = "/caf")
public class ProjectInfoController {
    @RequestMapping("/info")
    @ResponseBody
    public WebResult<Map<String, String>> getPayConfig() {
        Map<String, String> map = Maps.newHashMap();
        map.put("availableProcessors", String.valueOf(Runtime.getRuntime().availableProcessors()));
        return new WebResult<>(WebResult.CODE_SUCCESS, "OK", map);
    }
}
