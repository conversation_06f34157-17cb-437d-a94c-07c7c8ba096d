package com.coohua.caf.core.aliyun.cdn;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.support.BeanDefinitionRegistry;
import org.springframework.context.annotation.ImportBeanDefinitionRegistrar;
import org.springframework.core.annotation.AnnotationAttributes;
import org.springframework.core.type.AnnotationMetadata;
import org.springframework.util.Assert;

import com.coohua.caf.core.util.BeanRegistrationUtil;

public class AliyunCDNClientRegistrar implements ImportBeanDefinitionRegistrar {

	@Override
	public void registerBeanDefinitions(AnnotationMetadata importingClassMetadata, BeanDefinitionRegistry registry) {
		boolean processed = false;
		{
			AnnotationAttributes attributes = AnnotationAttributes
					.fromMap(importingClassMetadata.getAnnotationAttributes(EnableAliyunCDNClient.class.getName()));
			if (attributes != null) {
				dealOne(registry, attributes);
				processed = true;
			}
		}
		{
			AnnotationAttributes attributes = AnnotationAttributes
					.fromMap(importingClassMetadata.getAnnotationAttributes(EnableAliyunCDNClients.class.getName()));
			if (attributes != null) {
				AnnotationAttributes[] annotationArray = attributes.getAnnotationArray("value");
				if (annotationArray != null && annotationArray.length > 0) {
					for (AnnotationAttributes oneAttributes : annotationArray) {
						dealOne(registry, oneAttributes);
						processed = true;
					}
				}
			}
		}
		if (!processed)
			throw new IllegalStateException("no @EnableAliyunCDNClient or @EnableAliyunCDNClients found! pls check!");
	}

	private void dealOne(BeanDefinitionRegistry registry, AnnotationAttributes oneAttributes) {
		String bucketName = oneAttributes.getString("beanName");
		Assert.isTrue(StringUtils.isNotEmpty(bucketName), "namespace bucketName be specified !");
		String beanName = bucketName;

		BeanRegistrationUtil.registerBeanDefinitionIfBeanNameNotExists(registry, beanName,
				AliyunCDNClientFactory.class);
	}

}
