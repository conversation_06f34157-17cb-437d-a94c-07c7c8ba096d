package com.coohua.caf.core.rocketmq.core.consumer;

import com.coohua.caf.core.rocketmq.core.RocketMQBaseFactory;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2018/12/12.
 */
public class RocketMQBaseConsumerFactory extends RocketMQBaseFactory {

	public static final String PREFIX_APP_ROCKET_CONSUMER = "caf.mq.rocketmq.consumer";

	protected RocketMQConsumerConfig createRocketConsumerConfig() {
		RocketMQConsumerConfig config = new RocketMQConsumerConfig();
		return config;
	}

	protected String getRocketConsumerPrefix() {
		return PREFIX_APP_ROCKET_CONSUMER;
	}

}
