package com.coohua.caf.core.elasticsearch;

import org.springframework.context.annotation.Import;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Created by hepengyuan on 2018/12/12.
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
@Import(ElasticsearchTemplateRegistrar.class)
public @interface EnableElasticsearcheTemplates {

	EnableElasticsearchTemplate[] value();

}