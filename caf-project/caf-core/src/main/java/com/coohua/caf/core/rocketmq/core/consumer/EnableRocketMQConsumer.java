package com.coohua.caf.core.rocketmq.core.consumer;

import org.springframework.context.annotation.Import;

import java.lang.annotation.*;

/**
 * Created by he<PERSON><PERSON><PERSON> on 2018/12/12.
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
@Repeatable(EnableRocketMQConsumers.class)
@Import(RocketMQConsumerRegistrar.class)
public @interface EnableRocketMQConsumer {

	String namespace() default "default";

}