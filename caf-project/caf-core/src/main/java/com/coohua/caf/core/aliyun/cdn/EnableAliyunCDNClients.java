package com.coohua.caf.core.aliyun.cdn;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import org.springframework.context.annotation.Import;

@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
@Import(AliyunCDNClientRegistrar.class)
public @interface EnableAliyunCDNClients {

	EnableAliyunCDNClient[] value();
}
