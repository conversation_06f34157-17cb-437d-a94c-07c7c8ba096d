package com.coohua.caf.core.web;

import org.springframework.context.annotation.Import;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Created by zhangrongbin on 2019/4/26.
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
@Import(CHttpBioClientRegistrar.class)
public @interface EnableCHttpBioClients {
    EnableCHttpBioClient[] value();
}