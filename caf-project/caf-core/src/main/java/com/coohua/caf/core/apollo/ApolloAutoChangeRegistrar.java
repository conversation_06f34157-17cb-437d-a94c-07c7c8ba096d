package com.coohua.caf.core.apollo;

import java.util.HashSet;
import java.util.Set;

import org.springframework.beans.factory.support.BeanDefinitionRegistry;
import org.springframework.context.annotation.ImportBeanDefinitionRegistrar;
import org.springframework.core.annotation.AnnotationAttributes;
import org.springframework.core.type.AnnotationMetadata;

import com.coohua.caf.core.util.BeanRegistrationUtil;
import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;

public final class ApolloAutoChangeRegistrar implements ImportBeanDefinitionRegistrar {

	static Set<String> namespaceSet = new HashSet<String>();

	@Override
	public void registerBeanDefinitions(AnnotationMetadata importingClassMetadata, BeanDefinitionRegistry registry) {
		AnnotationAttributes enableApolloConfigAttributes = AnnotationAttributes
				.fromMap(importingClassMetadata.getAnnotationAttributes(EnableApolloConfig.class.getName()));

		if (enableApolloConfigAttributes == null) {
			return;
		}

		String[] namespaces = enableApolloConfigAttributes.getStringArray("value");
		if (namespaces != null) {
			for (String ns : namespaces) {
//				if (ns == null || ns.startsWith("caf.db.") || ns.startsWith("caf.rpc.") || ns.startsWith("caf.log.")
//						|| ns.startsWith("caf.redis.") || ns.startsWith("caf.rpc.") || ns.startsWith("caf.base.")) {
//					continue;
//				}
				namespaceSet.add(ns);
			}
		}

		BeanRegistrationUtil.registerBeanDefinitionIfBeanNameNotExists(registry,
				ApolloConfigAutoChangeProcessor.class.getSimpleName(), ApolloConfigAutoChangeProcessor.class);
	}

}
