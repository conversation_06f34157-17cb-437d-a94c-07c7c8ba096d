package com.coohua.caf.core.base;

import java.io.Serializable;

/**
 * Created by <PERSON><PERSON>ian<PERSON> on 2018/8/8.
 * comment: 由于与既有业务的字段名不一致，这里为data，既有业务中为result，特废弃此类，请迁移至{@link WebMessage}
 */
@Deprecated
public class WebResult<T> implements Serializable {

	private static final long serialVersionUID = 3937449233218325438L;

	public static final int CODE_SUCCESS = 0;
	public static final int CODE_FAILED = -1;
	public static final int CODE_LIMITED = -2;

	public static final String MSG_NONE = "";

	private int code = CODE_SUCCESS;
	private String msg = MSG_NONE;
	private T data;

	public WebResult() {}

	public static WebResult<String> block() {
		return new WebResult<>(CODE_LIMITED, "request blocked by sentinel!", "");
	}

	public static <T> WebResult<T> build(int code, String msg, T data) {
		return new WebResult<>(code, msg, data);
	}

	public WebResult(int code, String msg, T data) {
		this.code = code;
		this.msg = msg;
		this.data = data;
	}

	public int getCode() {
		return code;
	}

	public void setCode(int code) {
		this.code = code;
	}

	public String getMsg() {
		return msg;
	}

	public void setMsg(String msg) {
		this.msg = msg;
	}

	public T getData() {
		return data;
	}

	public void setData(T data) {
		this.data = data;
	}

	@Override
	public String toString() {
		return "WebResult{" + "code=" + code + ", msg='" + msg + '\'' + ", data=" + data + '}';
	}
}
