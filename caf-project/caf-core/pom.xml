<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<parent>
		<artifactId>caf-boot-parent</artifactId>
		<groupId>com.coohua.caf</groupId>
		<version>2.0.12-SNAPSHOT</version>
		<relativePath>../caf-boot-parent</relativePath>
	</parent>

	<modelVersion>4.0.0</modelVersion>
	<artifactId>caf-core</artifactId>

    <dependencies>
		<dependency>
			<groupId>top.zrbcool</groupId>
			<artifactId>pepper-metrics-druid</artifactId>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>top.zrbcool</groupId>
			<artifactId>pepper-metrics-mybatis</artifactId>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>top.zrbcool</groupId>
			<artifactId>pepper-metrics-servlet</artifactId>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>top.zrbcool</groupId>
			<artifactId>pepper-metrics-motan</artifactId>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>top.zrbcool</groupId>
			<artifactId>pepper-metrics-jedis</artifactId>
			<optional>true</optional>
		</dependency>
        <!-- sentinel -->
        <dependency>
            <groupId>com.alibaba.csp</groupId>
            <artifactId>sentinel-web-servlet</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>com.alibaba.csp</groupId>
            <artifactId>sentinel-core</artifactId>
            <optional>true</optional>
        </dependency>
        <!--<dependency>-->
            <!--<groupId>com.alibaba.csp</groupId>-->
            <!--<artifactId>sentinel-transport-simple-http</artifactId>-->
            <!--<optional>true</optional>-->
        <!--</dependency>-->
        <dependency>
            <groupId>com.alibaba.csp</groupId>
            <artifactId>ahas-sentinel-client</artifactId>
            <optional>true</optional>
        </dependency>
        <!-- http client-->
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpcore-nio</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpasyncclient</artifactId>
            <optional>true</optional>
        </dependency>
        <!-- aliyun oss -->
        <dependency>
            <groupId>com.aliyun.oss</groupId>
            <artifactId>aliyun-sdk-oss</artifactId>
        </dependency>

        <!-- aliyun cdn -->
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-java-sdk-cdn</artifactId>
        </dependency>

        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-java-sdk-core</artifactId>
        </dependency>

		<dependency>
			<groupId>io.micrometer</groupId>
			<artifactId>micrometer-registry-prometheus</artifactId>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-actuator</artifactId>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-aop</artifactId>
			<exclusions>
				<exclusion>
					<artifactId>spring-boot-starter-logging</artifactId>
					<groupId>org.springframework.boot</groupId>
				</exclusion>
			</exclusions>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
			<exclusions>
				<exclusion>
					<artifactId>spring-boot-starter-logging</artifactId>
					<groupId>org.springframework.boot</groupId>
				</exclusion>
			</exclusions>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>com.coohua.caf</groupId>
			<artifactId>caf-boot-starter-apollo</artifactId>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>com.coohua.caf</groupId>
			<artifactId>caf-boot-starter-elasticsearch</artifactId>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-configuration-processor</artifactId>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>fastjson</artifactId>
			<optional>true</optional>
		</dependency>
		<!-- mysql connector -->
		<dependency>
			<groupId>mysql</groupId>
			<artifactId>mysql-connector-java</artifactId>
			<optional>true</optional>
		</dependency>
		<!-- druid db connector pool -->
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>druid</artifactId>
			<optional>true</optional>
		</dependency>
		<!-- mybatis dao -->
		<dependency>
			<groupId>org.mybatis</groupId>
			<artifactId>mybatis</artifactId>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.mybatis</groupId>
			<artifactId>mybatis-spring</artifactId>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-jdbc</artifactId>
			<optional>true</optional>
		</dependency>
		<!-- motan -->
		<dependency>
			<groupId>com.weibo</groupId>
			<artifactId>motan-core</artifactId>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>com.weibo</groupId>
			<artifactId>motan-transport-netty</artifactId>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>com.weibo</groupId>
			<artifactId>motan-springsupport</artifactId>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>com.weibo</groupId>
			<artifactId>motan-registry-zookeeper</artifactId>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-core</artifactId>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-api</artifactId>
			<optional>true</optional>
		</dependency>
		<!-- Redis -->
		<dependency>
			<groupId>redis.clients</groupId>
			<artifactId>jedis</artifactId>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-collections4</artifactId>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>joda-time</groupId>
			<artifactId>joda-time</artifactId>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.joda</groupId>
			<artifactId>joda-convert</artifactId>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<optional>true</optional>
		</dependency>

		<!--prometheus -->
		<dependency>
			<groupId>io.prometheus</groupId>
			<artifactId>simpleclient</artifactId>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>io.prometheus</groupId>
			<artifactId>simpleclient_hotspot</artifactId>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>io.prometheus</groupId>
			<artifactId>simpleclient_httpserver</artifactId>
			<optional>true</optional>
		</dependency>

		<!-- rocketmq -->
		<dependency>
			<groupId>org.apache.rocketmq</groupId>
			<artifactId>rocketmq-client</artifactId>
			<optional>true</optional>
		</dependency>

		<!-- Test -->
		<dependency>
			<groupId>io.lettuce</groupId>
			<artifactId>lettuce-core</artifactId>
			<optional>true</optional>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<optional>true</optional>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-slf4j-impl</artifactId>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-webflux</artifactId>
			<exclusions>
				<exclusion>
					<artifactId>spring-boot-starter-logging</artifactId>
					<groupId>org.springframework.boot</groupId>
				</exclusion>
				<exclusion>
					<artifactId>spring-boot-starter-logging</artifactId>
					<groupId>org.springframework.boot</groupId>
				</exclusion>
			</exclusions>
			<optional>true</optional>
			<scope>test</scope>
		</dependency>
	</dependencies>
</project>
