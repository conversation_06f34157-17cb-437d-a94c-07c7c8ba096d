package com.coohua.app.sample.complex.service;

import static org.elasticsearch.index.query.QueryBuilders.termQuery;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.elasticsearch.core.ElasticsearchTemplate;
import org.springframework.data.elasticsearch.core.query.DeleteQuery;
import org.springframework.stereotype.Service;

import com.coohua.app.sample.complex.dao.es.mallESCluster.ShopESDao;
import com.coohua.app.sample.complex.dto.ShopDTO;
import com.coohua.app.sample.complex.model.es.mallESCluster.ShopESModel;

@Service
public class ShopService {

	@Autowired
	private ShopESDao shopESDao;

	@Resource(name = "elasticsearchTemplate:cluster.mall")
	private ElasticsearchTemplate elasticsearchTemplate;

	public void saveShop(ShopDTO shopDTO) {
		ShopESModel shopESModel = new ShopESModel();
		shopESModel.setShopBossId(shopDTO.getShopBossId() + "");
		shopESModel.setShopBossName(shopDTO.getShopBossName());
		shopESModel.setShopId(shopDTO.getShopId() + "");
		shopESModel.setShopName(shopDTO.getShopName());

		shopESDao.save(shopESModel);

	}

	public void fetchAllShops() {
		Iterable<ShopESModel> resultIterable = shopESDao.findAll();
		for (ShopESModel shop : resultIterable) {
			System.out.println(shop);
		}
	}

	public void deleteByShopIdUsingESTemplate(long shopId) {
		DeleteQuery deleteQuery = new DeleteQuery();
		deleteQuery.setIndex("shop_index");
		deleteQuery.setType("shop");
		deleteQuery.setQuery(termQuery("shopId", shopId));
		elasticsearchTemplate.delete(deleteQuery, ShopESModel.class);
		elasticsearchTemplate.refresh(ShopESModel.class);
		elasticsearchTemplate.delete(deleteQuery);
	}

}
