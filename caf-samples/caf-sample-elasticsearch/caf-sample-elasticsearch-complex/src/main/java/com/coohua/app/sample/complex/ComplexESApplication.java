package com.coohua.app.sample.complex;

import java.io.UnsupportedEncodingException;
import java.util.Random;

import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.data.elasticsearch.repository.config.EnableElasticsearchRepositories;
import org.springframework.stereotype.Component;

import com.coohua.app.sample.complex.dto.ShopDTO;
import com.coohua.app.sample.complex.dto.UserDTO;
import com.coohua.app.sample.complex.service.ShopService;
import com.coohua.app.sample.complex.service.UserService;
import com.coohua.caf.core.apollo.EnableAutoChangeApolloConfig;
import com.coohua.caf.core.elasticsearch.EnableElasticsearchTemplate;
import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;

import lombok.extern.slf4j.Slf4j;

/**
 * Created by hepengyuan on 2018/01/16.
 * 
 * -Denv=dev -Dapp.id=caf.es
 * 
 */

@EnableApolloConfig(value = { "application", "caf.es.cluster.user", "caf.es.cluster.mall",
		"caf.es.cluster.user.clientConfig", "caf.es.cluster.mall.clientConfig" })
@EnableAutoChangeApolloConfig
@SpringBootApplication
@Slf4j
public class ComplexESApplication {

	public static void main(String[] args) {
		SpringApplication.run(ComplexESApplication.class, args);
	}

	@Component
	class Runner implements ApplicationRunner, ApplicationContextAware {

		private ApplicationContext applicationContext;

		@Autowired
		private ShopService shopService;

		@Autowired
		private UserService userService;

		@Override
		public void run(ApplicationArguments args) {
			save();
			deleteUserByUserIdUsingESTemplate(973212109);
			deleteShopByShopIdUsingESTemplate(405179614);
		}

		private String[] RANDOM_STRING = new String[] { "a", "b", "c", "d", "e", "f", "g", "h", "i", "j", "k", "l", "m",
				"n", "o", "p", "q", "r", "s", "t", "u", "v", "w", "x", "y", "z" };
		private int LENGTH = RANDOM_STRING.length;

		private void save() {
			for (int i = 0; i < 10; i++) {
				ShopDTO shopDTO = new ShopDTO();
				shopDTO.setShopBossId((long) (Math.random() * Integer.MAX_VALUE));
				shopDTO.setShopBossName("shop boss name multi-es-cluster:" + getRandomString()
						+ (long) (Math.random() * Integer.MAX_VALUE));
				shopDTO.setShopId((long) (Math.random() * Integer.MAX_VALUE));
				shopDTO.setShopName("shop name:" + getRandomString() + (long) (Math.random() * Integer.MAX_VALUE));

				UserDTO userDTO = new UserDTO();
				userDTO.setUserId((long) (Math.random() * Integer.MAX_VALUE));
				userDTO.setFirstName("first name multi-es-cluster:" + getRandomString()
						+ (long) (Math.random() * Integer.MAX_VALUE));
				userDTO.setLastName("last name:" + getRandomString() + (long) (Math.random() * Integer.MAX_VALUE));

				shopService.saveShop(shopDTO);
				userService.saveUser(userDTO);
			}
			// shopService.fetchAllShops();
		}

		private String getRandomString() {
			StringBuilder sb = new StringBuilder();
			for (int i = 0; i < 5; i++) {
				int index = (int) (Math.random() * LENGTH);
				sb.append(RANDOM_STRING[index]);
			}
			sb.append(":");
			for (int i = 0; i < 5; i++) {
				sb.append(" ").append(getRandomChineseChar()).append(" ");
			}
			return sb.append(":").toString();
		}

		public String getRandomChineseChar() {
			String str = "";
			int highCode;
			int lowCode;

			Random random = new Random();

			highCode = (176 + Math.abs(random.nextInt(39))); // B0 + 0~39(16~55) 一级汉字所占区
			lowCode = (161 + Math.abs(random.nextInt(93))); // A1 + 0~93 每区有94个汉字

			byte[] b = new byte[2];
			b[0] = (Integer.valueOf(highCode)).byteValue();
			b[1] = (Integer.valueOf(lowCode)).byteValue();

			try {
				str = new String(b, "GBK");
			} catch (UnsupportedEncodingException e) {
				e.printStackTrace();
			}
			return str;
		}

		private void deleteShopByShopIdUsingESTemplate(long shopId) {
			shopService.deleteByShopIdUsingESTemplate(shopId);
		}

		private void deleteUserByUserIdUsingESTemplate(long userId) {
			userService.deleteByUserIdUsingESTemplate(userId);
		}

		@Override
		public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
			this.applicationContext = applicationContext;
		}
	}
}
