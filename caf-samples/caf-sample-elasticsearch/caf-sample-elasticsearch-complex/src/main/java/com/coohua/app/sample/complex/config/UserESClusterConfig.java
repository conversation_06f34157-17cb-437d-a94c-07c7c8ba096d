package com.coohua.app.sample.complex.config;

import org.springframework.data.elasticsearch.repository.config.EnableElasticsearchRepositories;
import org.springframework.stereotype.Component;

import com.coohua.caf.core.elasticsearch.EnableElasticsearchTemplate;

@EnableElasticsearchTemplate(cluster = "cluster.user", enableMultiElasticsearchSource = true)
@EnableElasticsearchRepositories(basePackages = "com.coohua.app.sample.complex.dao.es.userESCluster", elasticsearchTemplateRef = "elasticsearchTemplate:cluster.user")
@Component
public class UserESClusterConfig {

}
