package com.coohua.app.sample.complex.service;

import static org.elasticsearch.index.query.QueryBuilders.termQuery;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.elasticsearch.core.ElasticsearchTemplate;
import org.springframework.data.elasticsearch.core.query.DeleteQuery;
import org.springframework.stereotype.Service;

import com.coohua.app.sample.complex.dao.es.userESCluster.UserESDao;
import com.coohua.app.sample.complex.dto.UserDTO;
import com.coohua.app.sample.complex.model.es.userESCluster.UserESModel;

@Service
public class UserService {

	@Autowired
	private UserESDao userESDao;

	@Resource(name = "elasticsearchTemplate:cluster.user")
	private ElasticsearchTemplate elasticsearchTemplate;

	public void saveUser(UserDTO userDTO) {
		UserESModel userESModel = new UserESModel();
		userESModel.setFirstName(userDTO.getFirstName());
		userESModel.setLastName(userDTO.getLastName());
		userESModel.setUserId(userDTO.getUserId() + "");
		userESDao.save(userESModel);
	}

	public void deleteByUserIdUsingESTemplate(long userId) {
		DeleteQuery deleteQuery = new DeleteQuery();
		deleteQuery.setIndex("user_index");
		deleteQuery.setType("user");
		deleteQuery.setQuery(termQuery("userId", userId));
		elasticsearchTemplate.delete(deleteQuery, UserESModel.class);
		elasticsearchTemplate.refresh(UserESModel.class);
		elasticsearchTemplate.delete(deleteQuery);
	}

//	public void fetchAllCustomers() throws IOException {
//		System.out.println("Customers found with findAll():");
//		System.out.println("-------------------------------");
//		for (CustomerESModel customer : customerESDao.findAll()) {
//			System.out.println(customer);
//		}
//	}

}
