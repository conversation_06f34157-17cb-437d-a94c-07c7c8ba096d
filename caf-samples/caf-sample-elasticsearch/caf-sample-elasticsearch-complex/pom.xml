<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<parent>
		<artifactId>caf-boot-parent</artifactId>
		<groupId>com.coohua.caf</groupId>
		<version>2.0.12-SNAPSHOT</version>
		<relativePath>../../../caf-project/caf-boot-parent/pom.xml</relativePath>
	</parent>
	<modelVersion>4.0.0</modelVersion>
	<artifactId>caf-sample-elasticsearch-complex</artifactId>

	<dependencies>
		<dependency>
			<groupId>com.coohua.caf</groupId>
			<artifactId>caf-boot-starter</artifactId>
		</dependency>
		
		<dependency>
			<groupId>com.coohua.caf</groupId>
			<artifactId>caf-boot-starter-elasticsearch</artifactId>
		</dependency>
	</dependencies>

	<build>
<!-- 		<plugins> -->
<!-- 			<plugin> -->
<!-- 				<groupId>org.springframework.boot</groupId> -->
<!-- 				<artifactId>spring-boot-maven-plugin</artifactId> -->
<!-- 				<configuration> -->
<!-- 					<mainClass>com.coohua.app.sample.apollo.SimpleApolloApplication</mainClass> -->
<!-- 					<attach>false</attach> -->
<!-- 				</configuration> -->
<!-- 				<executions> -->
<!-- 					<execution> -->
<!-- 						<goals> -->
<!-- 							<goal>repackage</goal> -->
<!-- 						</goals> -->
<!-- 					</execution> -->
<!-- 				</executions> -->
<!-- 			</plugin> -->
<!-- 		</plugins> -->
	</build>
</project>