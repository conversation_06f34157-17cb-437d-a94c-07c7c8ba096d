package com.coohua.app.sample.simple.model.es;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.Document;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;

import java.io.Serializable;

/**
 * Created by lanmj on 2019/1/16.
 */
@Document(indexName = "mall_second", type = "order_second")
@Getter
@Setter
@ToString
public class OrderSecondESModel implements Serializable {

	private static final long serialVersionUID = -4893518773542114175L;

	@Id
	private String id;

	private String project;

	private Long orderId;

	private Long userId;

	private String code;

	private Integer createTime; // 下单时间

	private String secondRemark; // 二审原因，多个分开

	private Short status; // 对应订单的审核结果

	private String remark; // 审核备注

	private Integer auditTime; // 审核时间

	private String auditor; // 审核人

	@Override
	public String toString() {
		return JSON.toJSONString(this, SerializerFeature.PrettyFormat, SerializerFeature.WriteClassName);
	}
}
