package com.coohua.app.sample.simple.model.es;

import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.Document;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;

import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@Document(indexName = "user_index", type = "user", refreshInterval = "-1")
public class UserESModel {

	@Id
	private long userId;

	private String firstName;

	private String lastName;

	@Override
	public String toString() {
		return JSON.toJSONString(this, SerializerFeature.PrettyFormat, SerializerFeature.WriteClassName);
	}

}
