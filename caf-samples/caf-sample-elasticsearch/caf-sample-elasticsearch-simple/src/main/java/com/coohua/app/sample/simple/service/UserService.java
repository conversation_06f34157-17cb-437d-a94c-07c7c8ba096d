package com.coohua.app.sample.simple.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.coohua.app.sample.simple.dao.es.UserESDao;
import com.coohua.app.sample.simple.dto.UserDTO;
import com.coohua.app.sample.simple.model.es.UserESModel;

@Service
public class UserService {

	@Autowired
	private UserESDao userESDao;

	public void save(UserDTO userDTO) {
		UserESModel userESModel = new UserESModel();
		userESModel.setFirstName(userDTO.getFirstName());
		userESModel.setLastName(userDTO.getLastName());
		userESModel.setUserId(userDTO.getUserId());
		userESDao.save(userESModel);
	}

}
