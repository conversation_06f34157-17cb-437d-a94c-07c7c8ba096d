/**
 * <b>项目名：</b>mall-service<br/>
 * <b>包名：</b>com.coohua.mall.model<br/>
 * <b>文件名：</b>ProductCategory.java<br/>
 * <b>描述：</b><br/>
 * <b>版本信息：</b>v1.0.0<br/>
 * <b>日期：</b>2017年2月23日-下午4:31:18<br/>
 * <b>Copyright (c)</b> 2017<br/>
 */

package com.coohua.app.sample.simple.util;

import java.util.ArrayList;
import java.util.List;

/**
 * <b>类名称：</b>ProductCategory 商品分类<br/>
 * <b>类描述：</b><br/>
 * <b>创建人：</b>daiyong(<EMAIL>)<br/>
 * <b>修改人：</b><br/>
 * <b>修改时间：</b>2017年2月23日 下午4:31:18<br/>
 * <b>修改备注：</b><br/>
 * <b>版本信息：</b>v1.0.0<br/>
 */

public class ProductTypeConts {
	/**
	 * 未中奖商品
	 */
	public static final Integer NOT_WINNING = 0;
	/**
	 * 实物商品
	 */
	public static final Integer MATERIAL = 1;
	/**
	 * 支付宝提现商品
	 */
	public static final Integer AliPay = 2;
	/**
	 * 流量商品
	 */
	public static final Integer FLOW = 3;
	/**
	 * 大转盘
	 */
	public static final Integer DAZHUANPAN = 4;

	/**
	 * 积分
	 */
	public static final Integer CREDIT = 5;
	/**
	 * 微信提现
	 */
	public static final Integer WECHAT = 6;
	/**
	 * 小额微信提现
	 */
	public static final Integer SMALL_WECHAT = 7;
	/**
	 * QQB
	 */
	public static final Integer QQB = 8;
	/**
	 * 不输入验证码的直接微信提现
	 */
	public static final Integer DIRECT_WECHAT = 9;
	/**
	 * 不输入验证码的直接小额微信提现
	 */
	public static final Integer DIRECT_SMALL_WECHAT = 10;
	//start 为匹配shop添加的商品种类
	/**
	 * 卡密商品（现在是礼包，劵码之类的，以前的京东当当已经下架，及时处理）
	 */
	public static final Integer CARD_PWD = 11;
	/**
	 * 手机话费充值（向19Pay下单）,新的改为向ofei下单
	 */
	public static final Integer TEL_CHARGE = 12;
	/**
	 * 发放现金（已下架）
	 */
	public static final Integer CASH = 13;
	/**
	 * 银行卡转账（人工转账）
	 */
	public static final Integer CREDIT_CARD = 14;
	/**
	 * 支付宝码（向欧飞下单）
	 * @return
	 */
    public static final Integer ALIPAY_OFCARD = 15;
	/**
	 * 抽奖类单品（及时处理）
	 */
	public static final Integer LOTTERY = 16;
	/**
	 * 双色球
	 */
	public static final Integer LOTTERY_BALL = 17;
	/**
	 * 新闻赚需要审核的礼品，在商城审核后发放
	 */
	public static final Integer NEWS_AUDIT_AWARD = 18;
	/**
	 * 新闻赚需要审核的现金红包，在商城审核后发放
	 */
	public static final Integer NEWS_AUDIT_REDBAG = 19;

	//end 为匹配shop添加的商品种类

	/**
	 * 小额支付宝提现
	 */
	public static final Integer SMALL_ALIPAY = 20;

	/**
	 *额外微信提现（不算在消耗内）
	 */
	public static final Integer DIRECT_EXTRA_WECHAT = 21;

	/**
	 *额外支付宝提现（不算在消耗内）
	 */
	public static final Integer EXTRA_ALIPAY = 22;

	/**
	 *是否是支付宝类型
	 */
	public static boolean isAlipay(Integer productType){
		return AliPay.equals(productType) || SMALL_ALIPAY.equals(productType) || EXTRA_ALIPAY.equals(productType);
	}
	/**
	 *是否是现金类型
	 */
	public static boolean isCashType(Integer productType){
		return productType == AliPay
				|| productType == SMALL_ALIPAY
				|| productType == WECHAT
				|| productType == SMALL_WECHAT
				|| productType == DIRECT_WECHAT
				|| productType == DIRECT_SMALL_WECHAT
				|| productType == EXTRA_ALIPAY
				|| productType == DIRECT_EXTRA_WECHAT;
	}
	/**
	 *是否是微信类型
	 */
	public static boolean isWechat(Integer productType){
		return WECHAT.equals(productType) || SMALL_WECHAT.equals(productType) || DIRECT_WECHAT.equals(productType)
				|| DIRECT_SMALL_WECHAT.equals(productType) || DIRECT_EXTRA_WECHAT.equals(productType);
	}
	/**
	 *是否是微信类型
	 */
	public static boolean isCommonWechat(Integer productType){
		return WECHAT.equals(productType) || SMALL_WECHAT.equals(productType);
	}
	/**
	 *是否是B账户微信类型
	 */
	public static boolean isDirectWechat(Integer productType){
		return DIRECT_WECHAT.equals(productType) || DIRECT_SMALL_WECHAT.equals(productType) || DIRECT_EXTRA_WECHAT.equals(productType);
	}
	/**
	 *是否是大额现金类型
	 */
	public static boolean isBigCashProductType(Integer productType){
		return AliPay.equals(productType) || EXTRA_ALIPAY.equals(productType) || WECHAT.equals(productType) || DIRECT_WECHAT.equals(productType)
				|| DIRECT_EXTRA_WECHAT.equals(productType);
	}
	/**
	 *是否是大额支付宝类型
	 */
	public static boolean isBigAliProductType(Integer productType){
		return AliPay.equals(productType) || EXTRA_ALIPAY.equals(productType);
	}
	/**
	 *是否是大额微信类型
	 */
	public static boolean isBigWechatProductType(Integer productType){
		return WECHAT.equals(productType) || DIRECT_WECHAT.equals(productType) || DIRECT_EXTRA_WECHAT.equals(productType);
	}
	/**
	 * 是否是大额普通微信
	 */
	public static boolean isBigCommonWechatProductType(Integer productType){
		return WECHAT.equals(productType);
	}
	/**
	 * 是否是普通微信
	 */
	public static boolean isCommonWechatProductType(Integer productType){
		return WECHAT.equals(productType) || SMALL_WECHAT.equals(productType);
	}
	/**
	 *是否是大额微信类型
	 */
	public static boolean isBigDirectWechatProductType(Integer productType){
		return DIRECT_WECHAT.equals(productType) || DIRECT_EXTRA_WECHAT.equals(productType);
	}
	/**
	 *是否是大额微信类型
	 */
	public static boolean isDirectWechatProductType(Integer productType){
		return DIRECT_WECHAT.equals(productType) || DIRECT_EXTRA_WECHAT.equals(productType) || DIRECT_SMALL_WECHAT.equals(productType);
	}
	/**
	 *是否是不计消耗的类型
	 */
	public static boolean isNoSumProductType(Integer productType){
		return EXTRA_ALIPAY.equals(productType) || DIRECT_EXTRA_WECHAT.equals(productType);
	}

	/**
	 *获得所有支付宝类型
	 */
	public static List<Integer> getNeedAuditTypeList(){
		//小额直接打款的和验证码打款都不需要审核
		List<Integer> productTypeList = new ArrayList<>();
		//实物商品
		productTypeList.add(MATERIAL);
		//alipay
		productTypeList.add(AliPay);
		//FLOW
		productTypeList.add(FLOW);
		//WECHAT小额
		productTypeList.add(SMALL_WECHAT);
		//WECHAT
		productTypeList.add(WECHAT);
		//锁屏微信提现
		productTypeList.add(DIRECT_WECHAT);
		//锁屏小额微信提现
		productTypeList.add(DIRECT_SMALL_WECHAT);
		//QB
		productTypeList.add(QQB);
		//话费
		productTypeList.add(TEL_CHARGE);
		//小额支付宝提现
		productTypeList.add(SMALL_ALIPAY);
		//额外微信提现
		productTypeList.add(DIRECT_EXTRA_WECHAT);
		//额外支付宝提现
		productTypeList.add(EXTRA_ALIPAY);

		return productTypeList;
	}
	/**
	 *获得所有支付宝类型
	 */
	public static List<Integer> getAliTypeList(){
		List<Integer> list = new ArrayList<>();
		list.add(AliPay);
		list.add(SMALL_ALIPAY);
		list.add(EXTRA_ALIPAY);
		return list;
	}
	public static List<Short> getShortAliTypeList(){
		List<Short> list = new ArrayList<>();
		list.add(AliPay.shortValue());
		list.add(SMALL_ALIPAY.shortValue());
		list.add(EXTRA_ALIPAY.shortValue());
		return list;
	}
	/**
	 *获得所有账户微信类型
	 */
	public static List<Integer> getAllWechatTypeList(){
		List<Integer> list = new ArrayList<>();
		list.add(WECHAT);
		list.add(SMALL_WECHAT);
		list.add(DIRECT_EXTRA_WECHAT);
		list.add(DIRECT_WECHAT);
		list.add(DIRECT_SMALL_WECHAT);
		return list;
	}

	public static List<Short> getAllShortWechatTypeList(){
		List<Short> list = new ArrayList<>();
		list.add(WECHAT.shortValue());
		list.add(SMALL_WECHAT.shortValue());
		list.add(DIRECT_EXTRA_WECHAT.shortValue());
		list.add(DIRECT_WECHAT.shortValue());
		list.add(DIRECT_SMALL_WECHAT.shortValue());
		return list;
	}
	/**
	 *获得普通微信类型
	 */
	public static List<Integer> getCommonWechatTypeList(){
		List<Integer> list = new ArrayList<>();
		list.add(WECHAT);
		list.add(SMALL_WECHAT);
		return list;
	}
	/**
	 *获得B账户微信类型
	 */
	public static List<Integer> getDirectWechatTypeList(){
		List<Integer> list = new ArrayList<>();
		list.add(DIRECT_EXTRA_WECHAT);
		list.add(DIRECT_WECHAT);
		list.add(DIRECT_SMALL_WECHAT);
		return list;
	}
	/**
	 *获得大额支付宝类型
	 */
	public static List<Integer> getBigAliTypeList(){
		List<Integer> list = new ArrayList<>();
		list.add(AliPay);
		list.add(EXTRA_ALIPAY);
		return list;
	}
	/**
	 *获得大额微信类型
	 */
	public static List<Integer> getBigWechatTypeList(){
		List<Integer> list = new ArrayList<>();
		list.add(WECHAT);
		list.add(DIRECT_WECHAT);
		list.add(DIRECT_EXTRA_WECHAT);
		return list;
	}
	/**
	 *获得小额微信类型
	 */
	public static List<Integer> getSmallWechatTypeList(){
		List<Integer> list = new ArrayList<>();
		list.add(SMALL_WECHAT);
		list.add(DIRECT_SMALL_WECHAT);
		return list;
	}
	/**
	 *获得现金类型
	 */
	public static List<Integer> getCashProductTypes(){
		List<Integer> productTypes = new ArrayList<>();
		productTypes.add(AliPay);
		productTypes.add(SMALL_ALIPAY);
		productTypes.add(WECHAT);
		productTypes.add(SMALL_WECHAT);
		productTypes.add(DIRECT_WECHAT);
		productTypes.add(DIRECT_SMALL_WECHAT);
		productTypes.add(EXTRA_ALIPAY);
		productTypes.add(DIRECT_EXTRA_WECHAT);

		return productTypes;
	}
	/**
	 *获得现金类型
	 */
	public static List<Short> getCashShortProductTypes(){
		List<Short> productTypes = new ArrayList<>();
		productTypes.add(AliPay.shortValue());
		productTypes.add(SMALL_ALIPAY.shortValue());
		productTypes.add(WECHAT.shortValue());
		productTypes.add(SMALL_WECHAT.shortValue());
		productTypes.add(DIRECT_WECHAT.shortValue());
		productTypes.add(DIRECT_SMALL_WECHAT.shortValue());
		productTypes.add(EXTRA_ALIPAY.shortValue());
		productTypes.add(DIRECT_EXTRA_WECHAT.shortValue());

		return productTypes;
	}
    /**
     * 获得不计入消耗的类型
     */
    public static List<Integer> getNoSumProductTypes(){
        List<Integer> list = new ArrayList<>();
        list.add(DIRECT_EXTRA_WECHAT);
        list.add(EXTRA_ALIPAY);
        return list;
    }

    public static boolean isSmallCashType(Integer productType){
		return productType == SMALL_ALIPAY
				|| productType == SMALL_WECHAT
				|| productType == DIRECT_SMALL_WECHAT;
	}
}
