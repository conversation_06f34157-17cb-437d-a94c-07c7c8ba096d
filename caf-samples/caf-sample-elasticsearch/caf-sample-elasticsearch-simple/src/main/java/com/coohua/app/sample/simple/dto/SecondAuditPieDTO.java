package com.coohua.app.sample.simple.dto;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * Created by lanmj on 2019/1/23.
 */
@Setter
@Getter
@ToString
public class SecondAuditPieDTO {

	private String key; // 进审原因

	private Long percent; // 占比

	private Long passPercent; // 通过率

	@Override
	public String toString() {
		return JSON.toJSONString(this, SerializerFeature.PrettyFormat, SerializerFeature.WriteClassName);
	}
}
