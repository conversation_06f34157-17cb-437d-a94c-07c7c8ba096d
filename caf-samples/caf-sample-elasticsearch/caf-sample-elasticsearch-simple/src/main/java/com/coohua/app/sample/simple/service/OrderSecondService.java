package com.coohua.app.sample.simple.service;

import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.coohua.app.sample.simple.dao.es.OrderSecondESDao;
import com.coohua.app.sample.simple.model.es.OrderSecondESModel;

@Service
public class OrderSecondService {

	@Autowired
	private OrderSecondESDao orderSecondESDao;

	public OrderSecondESModel findById(String id) {
		Optional<OrderSecondESModel> optional = orderSecondESDao.findById(id);
		return (optional == null || optional.equals(Optional.empty())) ? null : optional.get();
	}
}
