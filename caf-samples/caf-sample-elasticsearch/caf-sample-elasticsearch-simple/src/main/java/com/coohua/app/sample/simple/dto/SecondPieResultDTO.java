package com.coohua.app.sample.simple.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.elasticsearch.search.aggregations.Aggregations;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;

/**
 * Created by lanmj on 2019/1/24.
 */
@Setter
@Getter
@ToString
public class SecondPieResultDTO {

	private long total;

	private Aggregations aggregations;

	@Override
	public String toString() {
		return JSON.toJSONString(this, SerializerFeature.PrettyFormat, SerializerFeature.WriteClassName);
	}
}
