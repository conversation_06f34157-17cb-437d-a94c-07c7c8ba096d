/**
 * <b>项目名：</b>mall-service<br/>
 * <b>包名：</b>com.coohua.mall.constant<br/>
 * <b>文件名：</b>OrderStatusConst2.java<br/>
 * <b>描述：</b><br/>
 * <b>版本信息：</b>v1.0.0<br/>
 * <b>日期：</b>2017年2月28日-上午10:26:31<br/>
 * <b>Copyright (c)</b> 2017<br/>
 */

package com.coohua.app.sample.simple.util;

/**
 * <b>类名称：</b>OrderStatusConst2 <br/>
 * <b>类描述：</b><br/>
 * <b>创建人：</b>daiyong(<EMAIL>)<br/>
 * <b>修改人：</b><br/>
 * <b>修改时间：</b>2017年2月28日 上午10:26:31<br/>
 * <b>修改备注：</b><br/>
 * <b>版本信息：</b>v1.0.0<br/>
 */

import java.util.ArrayList;
import java.util.List;

/**
 * 实物：下单1（审核中），待审核2（审核中），审核通过3（处理中），导出，已发货5（已发货），处理成功10（确认收货），审核不通过11（审核不通过），手动退款13（处理失败）
 * 提现：下单1（审核中），待审核2（审核中），审核通过3（处理中），导出，处理成功10（处理成功），审核不通过11（审核不通过），处理失败12（处理失败），手动退款13（处理失败）
 * 话费/流量：下单1（审核中），待审核2（审核中），审核通过3（处理中），调用第三方成功4（处理中），处理成功10（处理成功），审核不通过11（审核不通过），处理失败12（处理失败），手动退款13（处理失败）
 * 大转盘：下单1（未开奖），中奖7（中奖），未中奖8（未中奖） 
 */
public enum OrderStatusEnum {
	
	PLACE(1, "下单", "审核中"),
	PENDING_AUDIT(2, "待审核", "审核中"),
	AUDIT_PASS(3, "审核通过", "处理中"),
	CALL_THIRD_PARTY_SUCCESS(4, "调用第三方成功", "处理中"),
	DELIVERED(5, "已发货", "已发货"),
	WIN_PRIZE(7, "中奖", "中奖"),
	LOST_PRIZE(8, "未中奖", "未中奖"),
	PROCESS_SUCCESS(10, "处理成功", "处理成功"),
	AUDIT_NO_PASS(11, "审核不通过", "审核不通过"),
	PROCESS_FAIL(12, "处理失败", "处理失败"),
	DRAW_BACK(13, "手动退款", "处理失败")
	,SYSTEM_PASS_SECOND(14, "系统审单通过，需要二审", "人工复审(红字)")
	,SYSTEM_NOT_PASS_SECOND(15, "系统审单不通过，需要二审", "人工复审(黑字)")
	,ANTI_PENDING(16, "反作弊待处理", "反作弊待处理")
	,APPEAL(17, "申诉中", "申诉中")
	;
	
	private Integer code;
	
	private String innerMsg;
	
	private String outerMsg;

	/**
	 * @param code
	 * @param innerMsg
	 * @param outerMsg
	 */
	private OrderStatusEnum(Integer code, String innerMsg, String outerMsg) {
		this.code = code;
		this.innerMsg = innerMsg;
		this.outerMsg = outerMsg;
	}
	
	public Integer code() {
		return this.code;
	}

	public String innerMsg() {
		return this.innerMsg;
	}
	
	public String innerMsg(Integer code) {
		return this.innerMsg;
	}
	
	public String outerMsg() {
		return this.outerMsg;
	}
	
	public String outerMsg(Integer code) {
		return this.outerMsg;
	}

	public static boolean isAuditStatus(Integer status){
		if(AUDIT_PASS.code().equals(status)
				|| AUDIT_NO_PASS.code().equals(status)
				|| SYSTEM_PASS_SECOND.code().equals(status)
				|| SYSTEM_NOT_PASS_SECOND.code().equals(status)
				|| ANTI_PENDING.code().equals(status)){
			return true;
		}
		return false;
	}

	public static boolean checkStatusChange(Integer status, Integer productType, Integer afterStatus){
		if(status == afterStatus) {
			//实物商品的物流可以修改
			return afterStatus == OrderStatusEnum.DELIVERED.code() && productType == ProductTypeConts.MATERIAL;
		}
		//当前为反作弊待处理的订单，则状态随意
		if (status == ANTI_PENDING.code()){
			return true;
		}
		//退款状态
		if (afterStatus == DRAW_BACK.code()){
			if(productType == ProductTypeConts.MATERIAL) {
				return true;
			}
			if(status == PROCESS_SUCCESS.code()
					|| status == CALL_THIRD_PARTY_SUCCESS.code()){
				return false;
			}
		}

		//反作弊用户状态
		if (afterStatus == ANTI_PENDING.code()){
			//1、	户提现订单中，待审核、审核通过、人工复审（红字）状态的订单；
			//2、	用户话费流量订单中，待审核、审核通过、人工复审（红字）状态的订单；
			//3、	用户实物订单中，待审核、人工复审（红字）状态的订单
			if((ProductTypeConts.isCashType(productType) || productType == ProductTypeConts.FLOW || productType == ProductTypeConts.TEL_CHARGE)
				&& (status == PENDING_AUDIT.code() || status == AUDIT_PASS.code() || status == SYSTEM_PASS_SECOND.code())){
				return true;
			}else if(productType == ProductTypeConts.MATERIAL && (status == PENDING_AUDIT.code() || status == SYSTEM_PASS_SECOND.code())){
				return true;
			}
		}
		//仅审核不通过的订单可以申诉
		if(afterStatus == APPEAL.code() && status != AUDIT_NO_PASS.code()){
			return false;
		}
		//当前状态已经为处理结束状态
		if(status == DRAW_BACK.code()
				|| status == PROCESS_FAIL.code()
				|| status == PROCESS_SUCCESS.code()){
			return false;
		}
		return true;
	}

	public static List<Integer> getInvalidConsumerStatus(){
		List<Integer> statusList = new ArrayList<>(10);
		statusList.add(OrderStatusEnum.PLACE.code());
		statusList.add(OrderStatusEnum.PENDING_AUDIT.code());
		statusList.add(OrderStatusEnum.SYSTEM_PASS_SECOND.code());
		statusList.add(OrderStatusEnum.SYSTEM_NOT_PASS_SECOND.code());
		statusList.add(OrderStatusEnum.ANTI_PENDING.code());
		statusList.add(OrderStatusEnum.APPEAL.code());
		statusList.add(OrderStatusEnum.AUDIT_PASS.code());
		statusList.add(OrderStatusEnum.CALL_THIRD_PARTY_SUCCESS.code());
		statusList.add(OrderStatusEnum.DELIVERED.code());
		statusList.add(OrderStatusEnum.PROCESS_SUCCESS.code());
		return statusList;
	}
}
