package com.coohua.app.sample.simple.service;

import javax.annotation.Resource;

import org.apache.lucene.search.TermQuery;
import org.elasticsearch.index.query.QueryBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.elasticsearch.core.ElasticsearchTemplate;
import org.springframework.data.elasticsearch.core.query.DeleteQuery;
import org.springframework.stereotype.Service;

import com.coohua.app.sample.simple.dao.es.ShopESDao;
import com.coohua.app.sample.simple.dto.ShopDTO;
import com.coohua.app.sample.simple.model.es.ShopESModel;
import static org.elasticsearch.index.query.QueryBuilders.*;

@Service
public class ShopService {

	@Autowired
	private ShopESDao shopESDao;

	@Resource(name = "elasticsearchTemplate")
	private ElasticsearchTemplate elasticsearchTemplate;

	public void save(ShopDTO shopDTO) {
		ShopESModel shopESModel = new ShopESModel();
		shopESModel.setShopBossId(shopDTO.getShopBossId());
		shopESModel.setShopBossName(shopDTO.getShopBossName());
		shopESModel.setShopId(shopDTO.getShopId());
		shopESModel.setShopName(shopDTO.getShopName());

		shopESDao.save(shopESModel);
	}

	public void deleteByShopId(long shopId) {
		shopESDao.deleteById(shopId);
	}

	public void deleteByShopIdUsingESTemplate(long shopId) {
		DeleteQuery deleteQuery = new DeleteQuery();
		deleteQuery.setIndex("shop_index");
		deleteQuery.setType("shop");
		deleteQuery.setQuery(termQuery("shopId", shopId));
		elasticsearchTemplate.delete(deleteQuery, ShopESModel.class);
		elasticsearchTemplate.refresh(ShopESModel.class);
		elasticsearchTemplate.delete(deleteQuery);
	}

	public void fetchAllShops() {
		Iterable<ShopESModel> resultIterable = shopESDao.findAll();
		for (ShopESModel shop : resultIterable) {
			System.out.println("fetchAllShops:" + shop);
		}
	}

}
