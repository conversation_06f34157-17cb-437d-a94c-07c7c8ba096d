package com.coohua.app.sample.simple.model.es;

import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.Document;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;

import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@Document(indexName = "shop_index", type = "shop", refreshInterval = "-1")
public class ShopESModel {

	@Id
	private long shopId;

	private String shopName;

	private long shopBossId;

	private String shopBossName;

	@Override
	public String toString() {
		return JSON.toJSONString(this, SerializerFeature.PrettyFormat, SerializerFeature.WriteClassName);
	}

}
