package com.coohua.app.sample.simple;

import java.io.UnsupportedEncodingException;
import java.util.Random;

import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.data.elasticsearch.repository.config.EnableElasticsearchRepositories;
import org.springframework.stereotype.Component;

import com.coohua.app.sample.simple.dto.ShopDTO;
import com.coohua.app.sample.simple.dto.UserDTO;
import com.coohua.app.sample.simple.service.ShopService;
import com.coohua.app.sample.simple.service.UserService;
import com.coohua.caf.core.apollo.EnableAutoChangeApolloConfig;
import com.coohua.caf.core.elasticsearch.EnableElasticsearchTemplate;
import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;

import lombok.extern.slf4j.Slf4j;

/**
 * Created by hepengyuan on 2018/01/16.
 * 
 * -Denv=dev -Dapp.id=caf.es
 * 
 */

@EnableElasticsearchTemplate(cluster = "cluster1")
@EnableElasticsearchRepositories(basePackages = "com.coohua.app.sample.simple.dao.es")

@EnableApolloConfig(value = { "application", "caf.es.cluster1", "caf.es.cluster1.clientConfig" })
@EnableAutoChangeApolloConfig
@SpringBootApplication
@Slf4j
public class SimpleESApplication {

	public static void main(String[] args) {
		SpringApplication.run(SimpleESApplication.class, args);
	}

	@Component
	class Runner implements ApplicationRunner, ApplicationContextAware {

		private ApplicationContext applicationContext;

		@Autowired
		private ShopService shopService;

		@Autowired
		private UserService userService;

		@Override
		public void run(ApplicationArguments args) {
			save();
			deleteShopByShopIdUsingESTemplate(1401252444);
//			update(2132121886);
			// deleteShopByShopId(658524015);
			shopService.fetchAllShops();
		}

		private void save() {
			for (int i = 0; i < 10; i++) {
				ShopDTO shopDTO = new ShopDTO();
				shopDTO.setShopBossId((long) (Math.random() * Integer.MAX_VALUE));
				shopDTO.setShopBossName(
						"222 shop boss name:" + getRandomString() + (long) (Math.random() * Integer.MAX_VALUE));
				shopDTO.setShopId((long) (Math.random() * Integer.MAX_VALUE));
				shopDTO.setShopName("shop name:" + getRandomString() + (long) (Math.random() * Integer.MAX_VALUE));

				UserDTO userDTO = new UserDTO();
				userDTO.setUserId((long) (Math.random() * Integer.MAX_VALUE));
				userDTO.setFirstName(
						"333 first name:" + getRandomString() + (long) (Math.random() * Integer.MAX_VALUE));
				userDTO.setLastName("last name:" + getRandomString() + (long) (Math.random() * Integer.MAX_VALUE));

				System.out.println(userDTO);
				System.out.println(shopDTO);

				shopService.save(shopDTO);
				userService.save(userDTO);
			}
		}

		private void updateShop(long shopId) {
			ShopDTO shopDTO = new ShopDTO();
			shopDTO.setShopId(shopId);
			shopDTO.setShopBossName("lalalalala");
			shopService.save(shopDTO);
		}

		private void deleteShopByShopIdUsingESTemplate(long shopId) {
			System.out.println("deleteShopByShopIdUsingESTemplate");
			shopService.deleteByShopIdUsingESTemplate(shopId);
		}

		private String[] RANDOM_STRING = new String[] { "a", "b", "c", "d", "e", "f", "g", "h", "i", "j", "k", "l", "m",
				"n", "o", "p", "q", "r", "s", "t", "u", "v", "w", "x", "y", "z" };
		private int LENGTH = RANDOM_STRING.length;

		private String getRandomString() {
			StringBuilder sb = new StringBuilder();
			for (int i = 0; i < 5; i++) {
				int index = (int) (Math.random() * LENGTH);
				sb.append(RANDOM_STRING[index]);
			}
			sb.append(":");
			for (int i = 0; i < 5; i++) {
				sb.append(" ").append(getRandomChineseChar()).append(" ");
			}
			return sb.append(":").toString();
		}

		public String getRandomChineseChar() {
			String str = "";
			int highCode;
			int lowCode;

			Random random = new Random();

			highCode = (176 + Math.abs(random.nextInt(39))); // B0 + 0~39(16~55) 一级汉字所占区
			lowCode = (161 + Math.abs(random.nextInt(93))); // A1 + 0~93 每区有94个汉字

			byte[] b = new byte[2];
			b[0] = (Integer.valueOf(highCode)).byteValue();
			b[1] = (Integer.valueOf(lowCode)).byteValue();

			try {
				str = new String(b, "GBK");
			} catch (UnsupportedEncodingException e) {
				e.printStackTrace();
			}
			return str;
		}

		@Override
		public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
			this.applicationContext = applicationContext;
		}
	}
}
