<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>caf-sample-sentinel</artifactId>
        <groupId>com.coohua.caf</groupId>
        <version>2.0.12-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>caf-sample-sentinel-srv</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.coohua.caf</groupId>
            <artifactId>caf-boot-starter-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.coohua.caf</groupId>
            <artifactId>caf-boot-starter-web</artifactId>
			<exclusions>
				<exclusion>
					<artifactId>caf-boot-starter-apollo</artifactId>
					<groupId>com.coohua.caf</groupId>
				</exclusion>
				<exclusion>
					<artifactId>caf-boot-starter-rocketmq</artifactId>
					<groupId>com.coohua.caf</groupId>
				</exclusion>
				<exclusion>
					<artifactId>caf-boot-starter-kv</artifactId>
					<groupId>com.coohua.caf</groupId>
				</exclusion>
			</exclusions>
		</dependency>
        <dependency>
            <groupId>com.coohua.caf</groupId>
            <artifactId>caf-sample-sentinel-api</artifactId>
		</dependency>
    </dependencies>
	<!--<build>-->
		<!--<plugins>-->
			<!--<plugin>-->
				<!--<groupId>org.springframework.boot</groupId>-->
				<!--<artifactId>spring-boot-maven-plugin</artifactId>-->
				<!--<version>2.1.0.RELEASE</version>-->
				<!--<executions>-->
					<!--<execution>-->
						<!--<goals>-->
							<!--<goal>repackage</goal>-->
						<!--</goals>-->
					<!--</execution>-->
				<!--</executions>-->
			<!--</plugin>-->
		<!--</plugins>-->
	<!--</build>-->
</project>