package com.coohua.app.sample.sentinel.srv.service;

import com.alibaba.csp.sentinel.annotation.SentinelResource;
import com.alibaba.csp.sentinel.slots.block.BlockException;
import com.alibaba.csp.sentinel.slots.block.degrade.DegradeException;
import org.springframework.stereotype.Component;

@Component
public class SentinelAnnotationDemoImpl {

    @SentinelResource(value = "annotation", blockHandler = "annotationBlock")
    public String annotation(String param) {
        return "annotation success!";
    }

    public String annotationBlock(String param, BlockException ex) {
        if (ex instanceof DegradeException) {
            return "annotation degrade!!!";
        }
        return "annotation blocked!!!";
    }

}
