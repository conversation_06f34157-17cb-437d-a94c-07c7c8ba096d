package com.coohua.app.sample.sentinel.srv.controller;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.concurrent.TimeUnit;

/**
 * Created by zhangrongbin on 2018/11/29.
 */
@Slf4j
@RestController
@RequestMapping(value = "/sentinel", produces = "text/plain")
public class SentinelRestController {
    private static String B100 = StringUtils.repeat('b', 100);
    private static String B300 = StringUtils.repeat('b', 300);
    private static String B1_000 = StringUtils.repeat('b', 1_000);
    private static String B10_000 = StringUtils.repeat('b', 10_000);
    private static String B100_000 = StringUtils.repeat('b', 100_000);
    private static String B1000_000 = StringUtils.repeat('b', 1000_000);

    @RequestMapping("/latency/{latency}/bytes/{bytes}")
    public String latency(@PathVariable Integer latency, @PathVariable Integer bytes) throws InterruptedException {
        if (latency > 0)
            TimeUnit.MILLISECONDS.sleep(latency);
        return getBytes(bytes);
    }

    @RequestMapping("/simple")
    public String simple() throws InterruptedException {
        TimeUnit.MILLISECONDS.sleep(100);
        return getBytes(100);
    }


    private String getBytes(@PathVariable Integer bytes) {
        if (bytes > 0) {
            if (bytes == 100)
                return B100;
            else if (bytes == 1_000)
                return B1_000;
            else if (bytes == 10_000)
                return B10_000;
            else if (bytes == 100_000)
                return B100_000;
            else if (bytes == 1000_000)
                return B1000_000;
            else
                return StringUtils.repeat('b', bytes);
        }
        return "OK";
    }
}
