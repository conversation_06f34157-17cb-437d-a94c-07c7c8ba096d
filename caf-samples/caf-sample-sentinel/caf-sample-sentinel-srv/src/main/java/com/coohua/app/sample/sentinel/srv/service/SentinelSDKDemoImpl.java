package com.coohua.app.sample.sentinel.srv.service;

import com.alibaba.csp.sentinel.Entry;
import com.alibaba.csp.sentinel.SphU;
import com.alibaba.csp.sentinel.slots.block.BlockException;
import com.alibaba.csp.sentinel.slots.block.degrade.DegradeException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class SentinelSDKDemoImpl {
    public String sdk(String param) {
        Entry demoResource = null;
        String result = "sdk";
        try {
            demoResource = SphU.entry("demoResource");
            result = result + " > success";
        } catch (BlockException e) {
            // 根据异常类型判断发生熔断降级还是限流
            if (e instanceof DegradeException) {
                log.error("sdk method fallback");
                result = result + " > fallback";
            } else {
                log.error("sdk method blocked");
                result = result + " > blocked";
            }
        } finally {
            if (demoResource != null)
                demoResource.exit();
        }
        return result;
    }
}
