package com.coohua.app.sample.sentinel.srv.job;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class Runner implements ApplicationRunner {
    private static final ScheduledExecutorService EXECUTOR = Executors.newSingleThreadScheduledExecutor();
    @Override
    public void run(ApplicationArguments args) {
        EXECUTOR.scheduleAtFixedRate(() -> {
            log.info("this is a info log! -> " + System.currentTimeMillis());
            log.error("this is a error log! -> " + System.currentTimeMillis());
        }, 10, 10, TimeUnit.SECONDS);
    }
}
