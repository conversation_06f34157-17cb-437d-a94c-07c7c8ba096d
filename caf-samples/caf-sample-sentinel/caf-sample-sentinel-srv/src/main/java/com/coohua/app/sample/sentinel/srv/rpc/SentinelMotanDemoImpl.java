package com.coohua.app.sample.sentinel.srv.rpc;

import com.coohua.app.sample.sentinel.api.ISentinelMotanDemo;
import com.coohua.app.sample.sentinel.srv.service.SentinelAnnotationDemoImpl;
import com.weibo.api.motan.config.springsupport.annotation.MotanService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

@MotanService
@Slf4j
public class SentinelMotanDemoImpl implements ISentinelMotanDemo {
    @Autowired
    SentinelAnnotationDemoImpl sentinelAnnotationDemo;

    @Override
    public String motan(String param) {
        return "motan() > " + "annotation() > " + "return : " + sentinelAnnotationDemo.annotation(param);
    }

    @Override
    public String motanBlock(String param) {
        return "motan() > " + "motanBlock()";
    }

    @Override
    public String motanFallback(String param) {
        return "motan() > " + "motanFallback()";
    }
}
