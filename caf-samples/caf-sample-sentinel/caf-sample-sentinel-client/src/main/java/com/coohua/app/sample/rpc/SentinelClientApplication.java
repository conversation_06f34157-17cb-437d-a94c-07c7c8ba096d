package com.coohua.app.sample.rpc;

import com.alibaba.csp.sentinel.slots.block.BlockException;
import com.alibaba.csp.sentinel.slots.block.degrade.DegradeException;
import com.coohua.app.sample.sentinel.api.ISentinelMotanDemo;
import com.coohua.caf.core.rpc.EnableMotan;
import com.coohua.caf.core.web.CHttpBioClient;
import com.coohua.caf.core.web.CHttpClient;
import com.coohua.caf.core.web.EnableCHttpBioClient;
import com.coohua.caf.core.web.EnableCHttpClient;
import com.weibo.api.motan.config.springsupport.annotation.MotanReferer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpHeaders;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.UUID;
import java.util.concurrent.*;

/**
 * Created by zhangrongbin on 2018/09/27.
 */
@SpringBootApplication
@EnableMotan
@EnableCHttpClient
@EnableCHttpBioClient
@Slf4j
public class SentinelClientApplication {
    public static final ExecutorService EXECUTOR = Executors.newFixedThreadPool(10);
    public static void main(String[] args) {
        SpringApplication.run(SentinelClientApplication.class, args);
    }

    @Component
    class RunnerA implements ApplicationRunner {

        @Autowired
        private CHttpBioClient cHttpBioClient;

        private void post() {
            String REQ_ID = UUID.randomUUID().toString();
            String url = "https://sspjs1.schoolmall.top/ps/std?key=765266d8-07a3-4101-97bc-ae581116fb06";
            HttpResponse httpResponse = null;
            String reqContent="{\n" +
                    "\t\"appid\": 0,\n" +
                    "\t\"carrier\": 1,\n" +
                    "\t\"connectiontype\": 0,\n" +
                    "\t\"count\": 0,\n" +
                    "\t\"devicetype\": 1,\n" +
                    "\t\"did\": \"862652021022954\",\n" +
                    "\t\"dpid\": \"21d21aedf35bbf46\",\n" +
                    "\t\"id\": \"2148b8d756a143a8afba37e9e46c8\",\n" +
                    "\t\"ip\": \"***********\",\n" +
                    "\t\"key\": \"765266d8-07a3-4101-97bc-ae581116fb06\",\n" +
                    "\t\"lat\": 0.0,\n" +
                    "\t\"locationtype\": 0,\n" +
                    "\t\"lon\": 0.0,\n" +
                    "\t\"mac\": \"44%3A9E%3AF9%3A76%3A67%3AD1\",\n" +
                    "\t\"maker\": \"oppo\",\n" +
                    "\t\"model\": \"oppo\",\n" +
                    "\t\"os\": 4,\n" +
                    "\t\"osv\": \"25\",\n" +
                    "\t\"ua\": \"Mozilla/5.0 (Linux; Android 6.0.1; LEX720 Build/WAXCNFN6003009091S; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/66.0.3359.126 MQQBrowser/6.2 TBS/044611 Mobile Safari/537.36\",\n" +
                    "\t\"userid\": 0\n" +
                    "}";
            try {
                StringEntity entity = new StringEntity(reqContent, "utf-8");//解决中文乱码问题
                entity.setContentEncoding("UTF-8");
                entity.setContentType("application/json");
                HttpPost request = new HttpPost(url);
                request.setHeader(HttpHeaders.USER_AGENT, "Mozilla/5.0 (Linux; Android 6.0.1; LEX720 Build/WAXCNFN6003009091S; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/66.0.3359.126 MQQBrowser/6.2 TBS/044611 Mobile Safari/537.36");
                request.setEntity(entity);

                httpResponse = cHttpBioClient.execute(REQ_ID, request, 1000, TimeUnit.MILLISECONDS);
                final String toString = EntityUtils.toString(httpResponse.getEntity());
                log.info(toString);
            } catch (InterruptedException | ExecutionException | TimeoutException ignored ) {
                log.error(ignored.toString());
            } catch (BlockException e) {
                if (e instanceof DegradeException) {
                    //发生降级
                    log.error("", e);
                } else {
                    //发生限流
                    log.error("", e);
                }
            } catch (IOException e) {
                log.error("", e);
            } finally {
//                cHttpBioClient.closeResponseSilently(httpResponse);

                if (httpResponse != null) {
                    EntityUtils.consumeQuietly(httpResponse.getEntity());
                }
            }

        }

        @Override
        public void run(ApplicationArguments args) throws Exception {
            for (int i = 0; i < 5; i++) {
                post();
                TimeUnit.SECONDS.sleep(2);
            }
        }
    }
//    @Component
    class Runner implements ApplicationRunner {

        @MotanReferer
        private ISentinelMotanDemo sentinelMotanDemo;

        @Autowired
        private CHttpClient cHttpClient;

        @Autowired
        private CHttpBioClient cHttpBioClient;

        @Override
        public void run(ApplicationArguments args) throws InterruptedException {
            TimeUnit.SECONDS.sleep(10);
            for (int i = 0; i < 20; i++) {
                EXECUTOR.submit(() -> {
                    for (;;) {
                        try {
//                            motan();
                            http();
//                            post();
                        } catch (Exception ignored) {
                            log.error("", ignored);
                        } finally {
                            TimeUnit.MILLISECONDS.sleep(500);
                        }
                    }
                });
            }

        }

        private void http() {
            String REQ_ID = UUID.randomUUID().toString();
            HttpGet request = new HttpGet("http://localhost:9000/sentinel/latency/100/bytes/100");
            HttpResponse httpResponse = null;
            try {
                httpResponse = cHttpBioClient.execute(REQ_ID, request, 99, TimeUnit.MILLISECONDS);
                final HttpEntity entity = httpResponse.getEntity();
                //log.info(EntityUtils.toString(entity, "UTF-8"));
            } catch (InterruptedException | ExecutionException | TimeoutException ignored ) {
                log.error(ignored.toString());
            } catch (BlockException e) {
                if (e instanceof DegradeException) {
                    //发生降级
                    log.error("", e);
                } else {
                    //发生限流
                    log.error("", e);
                }
            } finally {
                cHttpBioClient.closeResponseSilently(httpResponse);
            }
        }


        private void motan() {
            String hello = sentinelMotanDemo.motan("robin");
            log.info("motan call return -> {}", hello);
        }
    }
}
