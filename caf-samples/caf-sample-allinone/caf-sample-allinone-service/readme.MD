(1).apollo配置中心:

app.logging.path = D:\logs\caf.rpc.allinone
app.motan.user.port = 1234
app.motan.mall.port = 1235

#
app.db.mall.data-source.url = jdbc:mysql://************:3306/mall?useUnicode=true&amp;characterEncoding=UTF-8&amp;autoReconnect=true
app.db.mall.data-source.username = root
app.db.mall.data-source.password = newPassword123@
app.db.mall.data-source.initial-size = 2
app.db.mall.data-source.max-active = 10
app.db.mall.data-source.max-idle = 16
app.db.mall.data-source.min-idle = 2
app.db.mall.type-aliases-package = com.coohua.allinone.rpc.model
#
app.db.user.data-source.url = ******************************************************************************************************
app.db.user.data-source.username = root
app.db.user.data-source.password = newPassword123@
app.db.user.data-source.initial-size = 2
app.db.user.data-source.max-active = 10
app.db.user.data-source.max-idle = 16
app.db.user.data-source.min-idle = 2
app.db.user.type-aliases-package = com.coohua.allinone.rpc.model
#
app.jedis-cluster.mall.address = ************:9700,************:9701,************:9702
app.jedis-cluster.mall.pool.max-total = 301
app.jedis-cluster.mall.pool.max-idle = 11
app.jedis-cluster.mall.pool.min-idle = 1
app.jedis-cluster.mall.pool.max-wait-millis = 6001

app.jedis-cluster.user.address = ************:9700,************:9702
app.jedis-cluster.user.pool.max-total = 301
app.jedis-cluster.user.pool.max-idle = 11
app.jedis-cluster.user.pool.min-idle = 1
app.jedis-cluster.user.pool.max-wait-millis = 6001
#
app.motan.user.registry.address = ************:2181
app.motan.mall.registry.address = ************:2181
或者配置：
app.motan.registry.address=************:2181



(2).样例运行程序需要增加启动参数:
使用如下参数启动不需要(1)中的配置，已经配好了。只在dev下可运行，其他环境没有做配置。
-Denv=dev -Dapp.id=caf.rpc.allinone

如果想要尝试修改配置，建立自己名字的集群，再复制一份相同配置，就可以随意CRUD，不会影响别人。
启动参数改为：
-Denv=dev -Dapp.id=caf.web.allinone -Dapollo.cluster=local-hepengyuan
