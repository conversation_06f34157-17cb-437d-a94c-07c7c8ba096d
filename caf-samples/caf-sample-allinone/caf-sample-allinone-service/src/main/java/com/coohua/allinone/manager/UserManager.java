package com.coohua.allinone.manager;

import com.alibaba.fastjson.TypeReference;
import com.coohua.allinone.constant.KVKeyConstant;
import com.coohua.allinone.mapper.userdb.UserMapper;
import com.coohua.allinone.model.UserModel;
import com.coohua.caf.core.kv.JedisClusterClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class UserManager {

	@Autowired
	private UserMapper userMapper;

	@Autowired
	private JedisClusterClient userJedisClusterClient;

	public UserModel findById(Long userId) {
		if (userId == null) {
			return null;
		}

		String key = String.format(KVKeyConstant.KEY_USER, userId);
		UserModel userModel = userJedisClusterClient.getCache(key, new TypeReference<UserModel>() {
		});

		if (userModel == null) {
			userModel = userMapper.findById(userId);
			if (userModel != null) {
				userJedisClusterClient.setCache(key, userModel);
			}
		}
		return userModel;
	}
}
