package com.coohua.allinone.remote.impl;

import org.springframework.beans.factory.annotation.Autowired;

import com.coohua.allinone.remote.api.UserRPC;
import com.coohua.allinone.constant.KVKeyConstant;
import com.coohua.allinone.remote.dto.UserDTO;
import com.coohua.allinone.remote.dto.UserDetailDTO;
import com.coohua.allinone.service.UserService;
import com.weibo.api.motan.config.springsupport.annotation.MotanService;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2018/09/27.
 */
@MotanService(basicService = "userBasicServiceConfigBean")
public class UserRPCImpl implements UserRPC {

	@Autowired
	private UserService userService;

	@Override
	public UserDetailDTO getUserDetail(Long userId) {
		// TODO 这里还有可能调用其他的RPC完成一些逻辑，同时也有可能添加user关联信息，然后组装DTO。
		return userService.getUserDetail(userId);
	}

	@Override
	public UserDTO getUser(Long userId) {
		// TODO 这里还有可能调用其他的RPC完成一些逻辑
		return userService.getUser(userId);
	}

	public static void main(String[] args) {
		System.out.println(String.format(KVKeyConstant.KEY_USER, 333));
	}
}
