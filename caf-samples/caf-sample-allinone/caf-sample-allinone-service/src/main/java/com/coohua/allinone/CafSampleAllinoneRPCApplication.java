package com.coohua.allinone;

import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

import javax.annotation.Resource;

import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;
import com.coohua.allinone.config.PayConfigBean;
import com.coohua.allinone.config.SMSConfigBean;
import com.coohua.allinone.config.SpideConfigBean;
import com.coohua.allinone.localcache.ShopModelCafWrapperLocalCache;
import com.coohua.allinone.localcache.ShopModelGuavaLocalCache;
import com.coohua.allinone.model.ShopModel;
import com.coohua.allinone.remote.dto.ShopDetailDTO;
import com.coohua.allinone.remote.dto.UserDetailDTO;
import com.coohua.allinone.service.ShopService;
import com.coohua.allinone.service.UserService;
import com.coohua.caf.core.apollo.EnableAutoChangeApolloConfig;
import com.coohua.caf.core.db.EnableDataSource;
import com.coohua.caf.core.kv.EnableJedisClusterClient;
import com.coohua.caf.core.rocketmq.consumer.RocketMQBaseConsumer;
import com.coohua.caf.core.rocketmq.core.consumer.EnableRocketMQConsumer;
import com.coohua.caf.core.rocketmq.core.producer.EnableRocketMQProducer;
import com.coohua.caf.core.rocketmq.producer.RocketMQBaseProducer;
import com.coohua.caf.core.rocketmq.producer.RocketMQSimpleProducer;
import com.coohua.caf.core.rpc.EnableMotan;
import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import com.google.common.collect.ImmutableMap;

import lombok.extern.slf4j.Slf4j;

/**
 * Created by hepengyuan on 2018/09/27.
 */
@SpringBootApplication

//开启两个redis-cluster实例
@EnableJedisClusterClient(namespace = "mall")
@EnableJedisClusterClient(namespace = "user")

//开启两个datasource实例
@EnableDataSource(namespace = "mall", mapperPackages = "com.coohua.allinone.mapper.malldb")
@EnableDataSource(namespace = "user", mapperPackages = "com.coohua.allinone.mapper.userdb")

//开启两个端口
@EnableMotan(namespace = "user")
@EnableMotan(namespace = "mall")

//开启rocketmq
@EnableRocketMQProducer(namespace = "ad-producer1")
@EnableRocketMQProducer(namespace = "ad-producer2")
@EnableRocketMQConsumer(namespace = "ad-consumer1-testTopic1")
@EnableRocketMQConsumer(namespace = "ad-consumer2-testTopic2")

//开启apollo配置中心
@EnableApolloConfig(value = { "application", "caf.db.user", "caf.db.mall", "caf.redis.cluster.rpc.user",
		"caf.redis.cluster.rpc.mall", "caf.biz.sms", "caf.biz.pay", "caf.biz.spide", "caf.mq.rocketmq.namesrv",
		"caf.mq.rocketmq.producer", "caf.mq.rocketmq.consumer" })
//系统启动时会打印@EnableApolloConfig中指定的namespace的初始化值；并且如果运行时会打印发生变化的配置。
@EnableAutoChangeApolloConfig

@Slf4j
public class CafSampleAllinoneRPCApplication {

	public static void main(String[] args) {
		SpringApplication.run(CafSampleAllinoneRPCApplication.class, args);
	}

	@Component
	class Runner implements ApplicationRunner, ApplicationContextAware {
		private ApplicationContext applicationContext;

		@Autowired
		private ShopService shopService;

		@Autowired
		private UserService userService;

		@Autowired
		private PayConfigBean payConfigBean;

		@Autowired
		private SMSConfigBean smsConfigBean;

		@Autowired
		private SpideConfigBean spideConfigBean;

		@Autowired
		private ShopModelGuavaLocalCache shopModelLocalCache;

		@Autowired
		private ShopModelCafWrapperLocalCache shopModelCafWrapperLocalCache;

		@Resource(name = "ad-producer1")
		private RocketMQBaseProducer adProducer1;

		@Resource(name = "ad-producer2")
		private RocketMQBaseProducer adProducer2;

		@Resource(name = "ad-consumer1-testTopic1")
		private RocketMQBaseConsumer adConsumer1TestTopic1;

		@Resource(name = "ad-consumer2-testTopic2")
		private RocketMQBaseConsumer adConsumer2TestTopic2;

		private static final String ROCKET_AD_TOPIC_TEST1 = "testTopic1";

		private static final String ROCKET_AD_TOPIC_TEST2 = "testTopic2";

		@Override
		public void run(ApplicationArguments args) throws Exception {

//			System.out.println(System.getProperty("user.home"));
//			
//			startConsumer1();
//			startConsumer2();
//
//			startProducer1();
//			startProducer2();

			while (true) {
				try {
//					ShopDetailDTO shopDTO = shopService.getShopDetail(1L);
//					log.info("shopDetail:" + shopDTO.toString());
//
//					UserDetailDTO userDTO = userService.getUserDetail(1L);
//					log.info("userDetail:" + userDTO.toString());
//
//					log.info("payConfigBean:" + payConfigBean.toString());
//
//					log.info("smsConfigBean:" + smsConfigBean.toString());
//
//					log.info("spideConfigBean:" + spideConfigBean.toString());

//					ShopModel shopModel = shopModelLocalCache.get(1L);
//					log.info(shopModel.toString());

//					List<Long> shopIdList = new ArrayList<Long>();
//					shopIdList.add(2L);
//					shopIdList.add(3L);
//					shopIdList.add(4L);
//					shopIdList.add(1L);
//
//					List<ShopModel> shopModelList = shopModelCafWrapperLocalCache.getAll(shopIdList);
//					log.info(JSONObject.toJSONString(shopModelList));
//
//					log.info(JSONObject.toJSONString("hpy:" + shopModelCafWrapperLocalCache.get(243242352L)));

//					SendResult sendResult1 = adProducer1.send(ROCKET_AD_TOPIC_TEST1,
//							"producer1-haha:" + System.currentTimeMillis());
//					System.out.println(sendResult1);
//
//					SendResult sendResult2 = adProducer2.send(ROCKET_AD_TOPIC_TEST2,
//							"producer2-haha:" + System.currentTimeMillis());
//					System.out.println(sendResult2);

					TimeUnit.SECONDS.sleep(1);
					// break;
				} catch (Exception e) {
					log.error(e.getMessage(), e);
					try {
						TimeUnit.SECONDS.sleep(3);
					} catch (InterruptedException e1) {
						// TODO Auto-generated catch block
						e1.printStackTrace();
					}
				}
			}
		}

		private void startConsumer1() throws Exception {
			adConsumer1TestTopic1.subscribe(ROCKET_AD_TOPIC_TEST1);
			adConsumer1TestTopic1.setConsumerGroup("testTopic1-consumerGroup");
			adConsumer1TestTopic1.registerMessageListener(new MessageListenerConcurrently() {

				@Override
				public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs,
						ConsumeConcurrentlyContext context) {
					// TODO biz code

					System.out.printf("Consuemr1 %s Receive New Messages: %s %n", Thread.currentThread().getName(),
							msgs);
					for (MessageExt ext : msgs) {
						try {
							System.out.printf("%s Receive New Message: %s %n", Thread.currentThread().getName(),
									new String(ext.getBody(), "UTF-8"));
						} catch (UnsupportedEncodingException e) {
							// TODO Auto-generated catch block
							e.printStackTrace();
						}
					}
					return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
				}
			});
			adConsumer1TestTopic1.start();
		}

		private void startConsumer2() throws Exception {
			adConsumer2TestTopic2.subscribe(ROCKET_AD_TOPIC_TEST2);
			adConsumer2TestTopic2.setConsumerGroup("testTopic2-consumerGroup");
			adConsumer2TestTopic2.registerMessageListener(new MessageListenerConcurrently() {

				@Override
				public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs,
						ConsumeConcurrentlyContext context) {
					// TODO biz code

					System.out.printf("Consuemr2 %s Receive New Messages: %s %n", Thread.currentThread().getName(),
							msgs);
					for (MessageExt ext : msgs) {
						try {
							System.out.printf("%s Receive New Message: %s %n", Thread.currentThread().getName(),
									new String(ext.getBody(), "UTF-8"));
						} catch (UnsupportedEncodingException e) {
							// TODO Auto-generated catch block
							e.printStackTrace();
						}
					}
					return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
				}
			});
			adConsumer2TestTopic2.start();
		}

		private void startProducer1() throws Exception {
			adProducer1.setProducerGroup("testTopic1-producerGroup");
			adProducer1.start();

		}

		private void startProducer2() throws Exception {
			adProducer2.setProducerGroup("testTopic2-producerGroup");
			adProducer2.start();
		}

		@Override
		public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
			this.applicationContext = applicationContext;
		}
	}
}
