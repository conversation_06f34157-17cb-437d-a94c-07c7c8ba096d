package com.coohua.allinone.remote.api;

import com.coohua.allinone.remote.dto.ShopDTO;
import com.coohua.allinone.remote.dto.ShopDetailDTO;

/**
 * Created by <PERSON>hangrongbin on 2018/09/27.
 */
public interface ShopRPC {

	/**
	 * 获取店铺基本信息
	 * 
	 * @param shopId
	 * @return
	 */
	public ShopDTO getShop(Long shopId);

	/**
	 * 获取店铺详细信息
	 * 
	 * @param shopId
	 * @return
	 */
	public ShopDetailDTO getShopDetail(Long shopId);
}
