package com.coohua.allinone.remote.api;

import com.coohua.allinone.remote.dto.UserDTO;
import com.coohua.allinone.remote.dto.UserDetailDTO;

/**
 * Created by <PERSON>hangrongbin on 2018/09/27.
 */
public interface UserRPC {

	/**
	 * 返回包含用户商铺信息的用户肖像
	 * 
	 * @param userId
	 * @return
	 */
	public UserDetailDTO getUserDetail(Long userId);

	/**
	 * 返回用户的基本肖像信息
	 * 
	 * @param userId
	 * @return
	 */
	public UserDTO getUser(Long userId);
}
