(1).apollo配置中心:
app.logging.path = D:\logs\caf.web.allinone

app.motan.userrpc.annotation.package = com.coohua.allinone.remote.api
app.motan.shoprpc.annotation.package = com.coohua.allinone.remote.api

app.motan.userrpc.registry.address = ************:2181
app.motan.shoprpc.registry.address = ************:2181
或者配置：
app.motan.registry.address=************:2181


(2).样例运行程序需要增加启动参数:
使用如下参数启动不需要(1)中的配置，已经配好了。只在dev下可运行，其他环境没有做配置。
-Denv=dev -Dapp.id=caf.web.allinone

如果想要尝试修改配置，建立自己名字的集群，再复制一份相同配置，就可以随意CRUD，不会影响别人。
启动参数改为：
-Denv=dev -Dapp.id=caf.web.allinone -Dapollo.cluster=local-hepengyuan

浏览器访问可以看到：
http://localhost:3333/user/getUserDetail?userId=1
http://localhost:3333/shop/getShopDetail?shopId=1
http://localhost:3333/config/getSMSConfig