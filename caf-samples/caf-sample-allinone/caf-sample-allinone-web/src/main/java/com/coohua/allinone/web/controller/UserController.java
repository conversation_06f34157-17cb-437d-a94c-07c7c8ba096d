package com.coohua.allinone.web.controller;

import com.coohua.allinone.remote.api.UserRPC;
import com.coohua.allinone.remote.dto.UserDTO;
import com.coohua.allinone.remote.dto.UserDetailDTO;
import com.coohua.caf.core.base.WebResult;
import com.weibo.api.motan.config.springsupport.annotation.MotanReferer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * Created by zhangrongbin on 2018/09/27.
 */
@RestController
@RequestMapping(value = "/user")
@Slf4j
public class UserController {

	@MotanReferer(basicReferer = "userBasicRefererConfigBean")
	private UserRPC userrpc;

	@RequestMapping("/getUser")
	public WebResult<UserDTO> getUser(@RequestParam(name = "userId") Long userId) {
		UserDTO userDTO = userrpc.getUser(userId);
		return new WebResult<>(WebResult.CODE_SUCCESS, "getUser success.", userDTO);
	}

	@RequestMapping("/getUserDetail")
	public WebResult<UserDetailDTO> getUserDetail(@RequestParam(name = "userId") Long userId) {
		UserDetailDTO userDTO = userrpc.getUserDetail(userId);
		return new WebResult<>(WebResult.CODE_SUCCESS, "getUserDetail success.", userDTO);
	}
}
