package com.coohua.allinone.web;

import com.coohua.allinone.web.service.ConfigService;
import com.coohua.caf.core.apollo.EnableAutoChangeApolloConfig;
import com.coohua.caf.core.rpc.EnableMotan;
import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;

import java.util.concurrent.TimeUnit;

import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

/**
 * Created by hepengyuan on 2018/09/27.
 */
@SpringBootApplication
@EnableApolloConfig(value = { "application", "caf.rpc.referer.user", "caf.rpc.referer.mall", "caf.biz.sms",
		"caf.biz.pay", "caf.biz.spide" })
@EnableAutoChangeApolloConfig
@EnableMotan(namespace = "user")
@EnableMotan(namespace = "mall")
public class CafSampleAllinoneWebApplication {

	public static void main(String[] args) {
		SpringApplication.run(CafSampleAllinoneWebApplication.class, args);
	}

	@Component
	class Runner implements ApplicationRunner, ApplicationContextAware {

		private ApplicationContext applicationContext;

		@Autowired
		private ConfigService configService;

		@Override
		public void run(ApplicationArguments args) throws Exception {
			while(true) {
				System.out.println(configService.getPayConfig());
				TimeUnit.SECONDS.sleep(1);
			}
		}

		@Override
		public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
			this.applicationContext = applicationContext;
		}
	}

}
