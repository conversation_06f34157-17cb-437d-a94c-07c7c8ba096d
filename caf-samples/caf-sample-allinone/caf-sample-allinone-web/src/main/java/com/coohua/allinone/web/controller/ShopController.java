package com.coohua.allinone.web.controller;

import com.coohua.allinone.remote.api.ShopRPC;
import com.coohua.allinone.remote.dto.ShopDTO;
import com.coohua.allinone.remote.dto.ShopDetailDTO;
import com.coohua.caf.core.base.WebResult;
import com.weibo.api.motan.config.springsupport.annotation.MotanReferer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/shop")
public class ShopController {

	private static final Logger logger = LoggerFactory.getLogger(ShopController.class);

	@MotanReferer(basicReferer = "mallBasicRefererConfigBean")
	private ShopRPC shoprpc;

	@RequestMapping("/getShop")
	public WebResult<ShopDTO> getShop(@RequestParam(name = "shopId") Long shopId) {
		ShopDTO shopDTO = shoprpc.getShop(shopId);
		return new WebResult<>(WebResult.CODE_SUCCESS, "getShop success.", shopDTO);
	}

	@RequestMapping("/getShopDetail")
	public WebResult<ShopDetailDTO> getShopDetail(@RequestParam(name = "shopId") Long shopId) {
		ShopDetailDTO shopDetailDTO = shoprpc.getShopDetail(shopId);
		return new WebResult<>(WebResult.CODE_SUCCESS, "getShopDetail success.", shopDetailDTO);
	}
}
