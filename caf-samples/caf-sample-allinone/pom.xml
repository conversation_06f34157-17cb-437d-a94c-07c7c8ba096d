<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<parent>
		<artifactId>caf-boot-parent</artifactId>
		<groupId>com.coohua.caf</groupId>
		<version>2.0.12-SNAPSHOT</version>
		<relativePath>../../caf-project/caf-boot-parent/pom.xml</relativePath>
	</parent>
	<modelVersion>4.0.0</modelVersion>
	<artifactId>caf-sample-allinone</artifactId>
	<packaging>pom</packaging>

	<modules>
		<module>caf-sample-allinone-service</module>
		<module>caf-sample-allinone-web</module>
		<module>caf-sample-allinone-api</module>
    </modules>
	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>com.coohua.caf</groupId>
				<artifactId>caf-sample-allinone-api</artifactId>
				<version>2.0.12-SNAPSHOT</version>
			</dependency>
		</dependencies>
	</dependencyManagement>

	<build>
		<pluginManagement>
			<plugins>
				<plugin>
					<groupId>org.eclipse.m2e</groupId>
					<artifactId>lifecycle-mapping</artifactId>
					<version>1.0.0</version>
					<configuration>
						<lifecycleMappingMetadata>
							<pluginExecutions>
								<pluginExecution>
									<pluginExecutionFilter>
										<groupId>org.codehaus.mojo</groupId>
										<artifactId>flatten-maven-plugin</artifactId>
										<versionRange>[1.0.0,)</versionRange>
										<goals>
											<goal>flatten</goal>
										</goals>
									</pluginExecutionFilter>
									<action>
										<ignore />
									</action>
								</pluginExecution>
							</pluginExecutions>
						</lifecycleMappingMetadata>
					</configuration>
				</plugin>
			</plugins>
		</pluginManagement>
	</build>
</project>