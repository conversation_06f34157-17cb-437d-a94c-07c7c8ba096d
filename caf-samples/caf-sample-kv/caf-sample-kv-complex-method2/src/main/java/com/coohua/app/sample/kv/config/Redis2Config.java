package com.coohua.app.sample.kv.config;

import com.coohua.caf.core.kv.AbstractJedisConfiguration;
import com.coohua.caf.core.kv.JedisClient;
import org.springframework.beans.factory.annotation.Autowire;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Created by <PERSON>hangrongbin on 2018/10/08.
 */
@Configuration
public class Redis2Config extends AbstractJedisConfiguration {

    @Override
    protected String namespace() {
        return "redis2";
    }

    @Override
    @Bean(name = "jedisClient2", autowire = Autowire.BY_NAME)
    protected JedisClient jedisClient() {
        return createJedisClient();
    }

}
