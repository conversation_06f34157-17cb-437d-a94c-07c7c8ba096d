package com.coohua.app.sample.kv.config;

import com.coohua.caf.core.kv.AbstractJedisConfiguration;
import com.coohua.caf.core.kv.JedisClient;
import org.springframework.beans.factory.annotation.Autowire;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Created by <PERSON>hangrongbin on 2018/10/08.
 */
@Configuration
public class Redis1Config extends AbstractJedisConfiguration {
    /**
     * 等同于绑定配置 prefix=app.jedis.redis1
     * app.jedis 是固定前缀, 在框架内写死, 如果要修改请重写getPrefix()方法
     */
    @Override
    protected String namespace() {
        return "redis1";
    }

    @Override
    @Bean(name = "jedisClient1", autowire = Autowire.BY_NAME)
    protected JedisClient jedisClient() {
        return createJedisClient();
    }

}
