package com.coohua.app.sample.kv;

import com.coohua.app.sample.kv.service.ComplexRedisSourceDemoService;
import org.springframework.beans.BeansException;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2018/09/27.
 */
@SpringBootApplication
public class ComplexKvMethod2Application {
    public static void main(String[] args) {
        SpringApplication.run(ComplexKvMethod2Application.class, args);
    }

    @Component
    class Runner implements ApplicationRunner, ApplicationContextAware {
        private ApplicationContext applicationContext;
        @Override
        public void run(ApplicationArguments args) {
            ComplexRedisSourceDemoService service = applicationContext.getBean(ComplexRedisSourceDemoService.class);
            service.helloRedis1();
            service.helloRedis2();

            service.helloRedisCluster1();
            service.helloRedisCluster2();
        }

        @Override
        public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
            this.applicationContext = applicationContext;
        }
    }
}
