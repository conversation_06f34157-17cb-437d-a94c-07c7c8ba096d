package com.coohua.app.sample.kv.config;

import com.coohua.caf.core.kv.AbstractJedisClusterConfiguration;
import com.coohua.caf.core.kv.JedisClusterClient;
import org.springframework.beans.factory.annotation.Autowire;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2018/10/08.
 */
@Configuration
public class RedisCluster1Config extends AbstractJedisClusterConfiguration {

    @Override
    protected String namespace() {
        return "cluster1";
    }

    @Override
    @Bean(name = "jedisClusterClient1", autowire = Autowire.BY_NAME)
    public JedisClusterClient jedisClusterClient() throws NoSuchFieldException {
        return createJedisClusterClient();
    }

}
