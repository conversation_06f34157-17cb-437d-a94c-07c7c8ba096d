(1).apollo配置中心，私有空间application需要配置：
app.logging.path = D:\logs\cafSingleRedisApolloServer
app.jedis.default.address = ************
app.jedis.default.port = 6379
app.jedis-cluster.default.address = ************:9700,************:9701,1************:9702

(2).样例运行程序需要增加启动参数:

-Denv=dev -Dapollo.cluster=default -Dapp.id=cafSingleRedisApolloServer -Dapollo.meta=http://apollo-dev-1:8080,http://apollo-dev-2:8081,http://apollo-dev-3:8082