package com.coohua.app.sample.kv.service;

import com.coohua.caf.core.kv.JedisClient;
import com.coohua.caf.core.kv.JedisClusterClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

/**
 * Created by zhangrongbin on 2018/10/08.
 */
@Service
public class SingleRedisSourceDemoService {
    private static final Logger logger = LoggerFactory.getLogger(SingleRedisSourceDemoService.class);
    @Autowired
    @Qualifier("defaultJedisClient")
    private JedisClient defaultJedisClient;
    @Autowired
    private JedisClusterClient jedisClusterClient;

    public String helloRedis() {
        logger.info("SingleRedisSourceDemoService ... helloRedis()");
        defaultJedisClient.set("hello", "jedis");
        String helloString = "helloRedis()! from jedis: [hello=" + defaultJedisClient.get("hello") + "]";
        logger.info(helloString);
        return helloString;
    }

    public String helloRedisCluster() {
        logger.info("SingleRedisSourceDemoService ... helloRedisCluster()");
        jedisClusterClient.set("hello", "jedisCluster");
        String helloString = "helloRedisCluster()! from jedisCluster: [hello=" +
                jedisClusterClient.get("hello") +  "]";
        logger.info(helloString);
        return helloString;
    }

}
