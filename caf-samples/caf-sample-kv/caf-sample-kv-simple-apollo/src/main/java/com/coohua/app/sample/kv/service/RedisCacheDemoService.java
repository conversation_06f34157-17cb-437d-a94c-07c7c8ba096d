package com.coohua.app.sample.kv.service;

import com.alibaba.fastjson.TypeReference;
import com.coohua.app.sample.kv.model.User;
import com.coohua.caf.core.kv.JedisClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by z<PERSON><PERSON><PERSON> on 2018/10/08.
 */
@Service
public class RedisCacheDemoService {
    private static final Logger logger = LoggerFactory.getLogger(RedisCacheDemoService.class);
    @Autowired
    private JedisClient defaultJedisClient;

    public String helloCache() {
        int intTo = 100;
        defaultJedisClient.setCache("intTo", intTo);
        Integer intFrom = defaultJedisClient.getCache("intTo", new TypeReference<Integer>() {});
        logger.info("to: {} --> from {}", intTo, intFrom);

        long longTo = 120L;
        defaultJedisClient.setCache("longTo", longTo);
        Long longFrom = defaultJedisClient.getCache("longTo", new TypeReference<Long>() {});
        logger.info("to: {} --> from {}", longTo, longFrom);

        float fTo = 123.123f;
        defaultJedisClient.setCache("fTo", fTo);
        Float fFrom = defaultJedisClient.getCache("fTo", new TypeReference<Float>() {});
        logger.info("to: {} --> from {}", fTo, fFrom);

        double dTo = 321.321d;
        defaultJedisClient.setCache("dTo", dTo);
        Double dFrom = defaultJedisClient.getCache("dTo", new TypeReference<Double>() {});
        logger.info("to: {} --> from {}", dTo, dFrom);

        boolean booleanTo = false;
        defaultJedisClient.setCache("booleanTo", booleanTo);
        Boolean booleanFrom = defaultJedisClient.getCache("booleanTo", new TypeReference<Boolean>() {});
        logger.info("to: {} --> from {}", booleanTo, booleanFrom);

        short sTo = 1111;
        defaultJedisClient.setCache("sTo", sTo);
        Short sFrom = defaultJedisClient.getCache("sTo", new TypeReference<Short>() {});
        logger.info("to: {} --> from {}", sTo, sFrom);

        byte byteTo = 111;
        defaultJedisClient.setCache("byteTo", byteTo);
        Byte byteFrom = defaultJedisClient.getCache("byteTo", new TypeReference<Byte>() {});
        logger.info("to: {} --> from {}", byteTo, byteFrom);

        char charTo = 'A';
        defaultJedisClient.setCache("charTo", charTo);
        Character charFrom = defaultJedisClient.getCache("charTo", new TypeReference<Character>() {});
        logger.info("to: {} --> from {}", charTo, charFrom);

        // complex samples
        Map map = new HashMap();
        map.put("intTo", intTo);
        map.put("longTo", longTo);
        map.put("byteTo", byteTo);
        map.put("fTo", fTo);
        map.put("dTo", dTo);
        map.put("charTo", charTo);
        map.put("sTo", sTo);
        map.put("booleanTo", booleanTo);
        defaultJedisClient.setCache("mapWithPrimitive", map);
        Map mapFrom = defaultJedisClient.getCache("mapWithPrimitive", new TypeReference<Map>() {});
        logger.info("to  : {}", map);
        logger.info("from: {}", mapFrom);
        mapFrom.forEach((key, value) -> logger.info("key: {}, value: {}, type: {}", key, value, value.getClass()));

        //generic type
        Map<String, List<List<User>>> map2 = new HashMap<>();
        List<User> users = new ArrayList<>();
        users.add(User.createUser());
        users.add(User.createUser());
        List<User> users2 = new ArrayList<>();
        users.add(User.createUser());
        users.add(User.createUser());
        List<List<User>> list = new ArrayList<>();
        list.add(users);
        list.add(users2);
        map2.put("list", list);
        defaultJedisClient.setCache("genericMap", map2);
        Map<String, List<List<User>>> map2From = defaultJedisClient.getCache("genericMap", new TypeReference<Map<String, List<List<User>>>>() {});
        logger.info("to  : {}", map2);
        logger.info("from: {}", map2From);
        map2From.forEach((key, value) -> logger.info("key: {}, value: {}, type: {}", key, value, value.getClass()));

        return "done";
    }

}
