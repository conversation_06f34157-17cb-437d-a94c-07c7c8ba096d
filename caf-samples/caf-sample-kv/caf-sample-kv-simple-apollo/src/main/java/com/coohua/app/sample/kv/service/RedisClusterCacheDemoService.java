package com.coohua.app.sample.kv.service;

import com.alibaba.fastjson.TypeReference;
import com.coohua.app.sample.kv.model.User;
import com.coohua.caf.core.kv.JedisClusterClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by z<PERSON><PERSON><PERSON> on 2018/10/08.
 */
@Service
public class RedisClusterCacheDemoService {
    private static final Logger logger = LoggerFactory.getLogger(RedisClusterCacheDemoService.class);
    @Autowired
    private JedisClusterClient jedisClusterClient;

    public String helloCache() {
        int intTo = 100;
        jedisClusterClient.setCache("intTo", intTo);
        Integer intFrom = jedisClusterClient.getCache("intTo", new TypeReference<Integer>() {});
        logger.info("to: {} --> from {}", intTo, intFrom);

        long longTo = 120L;
        jedisClusterClient.setCache("longTo", longTo);
        Long longFrom = jedisClusterClient.getCache("longTo", new TypeReference<Long>() {});
        logger.info("to: {} --> from {}", longTo, longFrom);

        float fTo = 123.123f;
        jedisClusterClient.setCache("fTo", fTo);
        Float fFrom = jedisClusterClient.getCache("fTo", new TypeReference<Float>() {});
        logger.info("to: {} --> from {}", fTo, fFrom);

        double dTo = 321.321d;
        jedisClusterClient.setCache("dTo", dTo);
        Double dFrom = jedisClusterClient.getCache("dTo", new TypeReference<Double>() {});
        logger.info("to: {} --> from {}", dTo, dFrom);

        boolean booleanTo = false;
        jedisClusterClient.setCache("booleanTo", booleanTo);
        Boolean booleanFrom = jedisClusterClient.getCache("booleanTo", new TypeReference<Boolean>() {});
        logger.info("to: {} --> from {}", booleanTo, booleanFrom);

        short sTo = 1111;
        jedisClusterClient.setCache("sTo", sTo);
        Short sFrom = jedisClusterClient.getCache("sTo", new TypeReference<Short>() {});
        logger.info("to: {} --> from {}", sTo, sFrom);

        byte byteTo = 111;
        jedisClusterClient.setCache("byteTo", byteTo);
        Byte byteFrom = jedisClusterClient.getCache("byteTo", new TypeReference<Byte>() {});
        logger.info("to: {} --> from {}", byteTo, byteFrom);

        char charTo = 'A';
        jedisClusterClient.setCache("charTo", charTo);
        Character charFrom = jedisClusterClient.getCache("charTo", new TypeReference<Character>() {});
        logger.info("to: {} --> from {}", charTo, charFrom);

        // complex samples
        Map map = new HashMap();
        map.put("intTo", intTo);
        map.put("longTo", longTo);
        map.put("byteTo", byteTo);
        map.put("fTo", fTo);
        map.put("dTo", dTo);
        map.put("charTo", charTo);
        map.put("sTo", sTo);
        map.put("booleanTo", booleanTo);
        jedisClusterClient.setCache("mapWithPrimitive", map);
        Map mapFrom = jedisClusterClient.getCache("mapWithPrimitive", new TypeReference<Map>() {});
        logger.info("to  : {}", map);
        logger.info("from: {}", mapFrom);
        mapFrom.forEach((key, value) -> logger.info("key: {}, value: {}, type: {}", key, value, value.getClass()));

        //generic type
        Map<String, List<List<User>>> map2 = new HashMap<>();
        List<User> users = new ArrayList<>();
        users.add(User.createUser());
        users.add(User.createUser());
        List<User> users2 = new ArrayList<>();
        users.add(User.createUser());
        users.add(User.createUser());
        List<List<User>> list = new ArrayList<>();
        list.add(users);
        list.add(users2);
        map2.put("list", list);
        jedisClusterClient.setCache("genericMap", map2);
        Map<String, List<List<User>>> map2From = jedisClusterClient.getCache("genericMap", new TypeReference<Map<String, List<List<User>>>>() {});
        logger.info("to  : {}", map2);
        logger.info("from: {}", map2From);
        map2From.forEach((key, value) -> logger.info("key: {}, value: {}, type: {}", key, value, value.getClass()));

        return "done";
    }

}
