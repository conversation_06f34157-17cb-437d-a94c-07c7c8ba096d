package com.coohua.app.sample.kv;

import java.util.concurrent.TimeUnit;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import com.coohua.app.sample.kv.service.RedisCacheDemoService;
import com.coohua.app.sample.kv.service.RedisClusterCacheDemoService;
import com.coohua.app.sample.kv.service.SingleRedisSourceDemoService;
import com.coohua.caf.core.kv.EnableJedisClient;
import com.coohua.caf.core.kv.EnableJedisClusterClient;
import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;

/**
 * Created by z<PERSON><PERSON><PERSON> on 2018/09/27.
 */
@SpringBootApplication
@EnableJedisClient
@EnableJedisClusterClient
@EnableApolloConfig
public class SimpleKvApolloApplication {

	public static void main(String[] args) {
		SpringApplication.run(SimpleKvApolloApplication.class, args);
	}

	private static final Logger logger = LoggerFactory.getLogger(SimpleKvApolloApplication.class);

	@Component
	class Runner implements ApplicationRunner, ApplicationContextAware {
		private ApplicationContext applicationContext;

		@Override
		public void run(ApplicationArguments args) {
			while (true) {
				try {
					SingleRedisSourceDemoService service = applicationContext
							.getBean(SingleRedisSourceDemoService.class);
					service.helloRedis();
					service.helloRedisCluster();

					RedisCacheDemoService cacheDemoService = applicationContext.getBean(RedisCacheDemoService.class);
					cacheDemoService.helloCache();

					RedisClusterCacheDemoService cacheClusterDemoService = applicationContext
							.getBean(RedisClusterCacheDemoService.class);
					cacheClusterDemoService.helloCache();

					TimeUnit.SECONDS.sleep(1);
//					break;
				} catch (Exception e) {
					logger.error(e.getMessage(), e);
				}
			}
		}

		@Override
		public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
			this.applicationContext = applicationContext;
		}
	}
}
