package com.coohua.app.sample.kv.model;

import org.apache.commons.lang3.RandomUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2018/10/08.
 */
public class User implements Serializable {
    private String name;
    private int age;
    private List<Address> addressList = new ArrayList<>();
    private float money = 123.123f;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getAge() {
        return age;
    }

    public void setAge(int age) {
        this.age = age;
    }

    public List<Address> getAddressList() {
        return addressList;
    }

    public void setAddressList(List<Address> addressList) {
        this.addressList = addressList;
    }

    public float getMoney() {
        return money;
    }

    public void setMoney(float money) {
        this.money = money;
    }

    @Override
    public String toString() {
        return "User{" +
                "name='" + name + '\'' +
                ", age=" + age +
                ", addressList=" + addressList +
                ", money=" + money +
                '}';
    }

    public static User createUser() {
        User user = new User();
        user.setName("robin-" + System.currentTimeMillis());
        user.setAge(RandomUtils.nextInt());
        user.getAddressList().add(new Address(RandomUtils.nextInt(), "北京"));
        user.getAddressList().add(new Address(RandomUtils.nextInt(), "长春"));
        return user;
    }
}
