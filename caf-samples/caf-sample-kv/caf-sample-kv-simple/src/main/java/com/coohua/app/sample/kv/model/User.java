package com.coohua.app.sample.kv.model;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.lang3.RandomUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by zhangrongbin on 2018/10/08.
 */
@Setter
@Getter
@ToString
public class User implements Serializable {
    private String name;
    private int age;
    private List<Address> addressList = new ArrayList<>();
    private float money = 123.123f;

    public static User createUser() {
        User user = new User();
        user.setName("robin-" + System.currentTimeMillis());
        user.setAge(RandomUtils.nextInt());
        user.getAddressList().add(new Address(RandomUtils.nextInt(), "北京"));
        user.getAddressList().add(new Address(RandomUtils.nextInt(), "长春"));
        return user;
    }
}
