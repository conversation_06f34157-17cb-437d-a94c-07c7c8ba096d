package com.coohua.app.sample.kv.service;

import com.coohua.caf.core.kv.JedisClient;
import com.coohua.caf.core.kv.JedisClusterClient;
import com.coohua.caf.core.metrics.Profile;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Created by z<PERSON><PERSON><PERSON> on 2018/10/08.
 */
@Service
@Slf4j
public class SingleRedisSourceDemoService {
    @Autowired
    private JedisClient defaultJedisClient;
//    @Autowired
    private JedisClusterClient jedisClusterClient;

    public void warmUp() {
        defaultJedisClient.warmUp();
    }

    public String helloRedis() {
        log.info("SingleRedisSourceDemoService ... helloRedis()");
        defaultJedisClient.set("hello", "jedis");
        String helloString = "helloRedis()! from jedis: [hello=" + defaultJedisClient.get("hello") + "]";
        log.info(helloString);
        return helloString;
    }

    public String helloRedisCluster() {
        log.info("SingleRedisSourceDemoService ... helloRedisCluster()");
        jedisClusterClient.set("hello", "jedisCluster");
        String helloString = "helloRedisCluster()! from jedisCluster: [hello=" +
                jedisClusterClient.get("hello") +  "]";
        log.info(helloString);
        return helloString;
    }

}
