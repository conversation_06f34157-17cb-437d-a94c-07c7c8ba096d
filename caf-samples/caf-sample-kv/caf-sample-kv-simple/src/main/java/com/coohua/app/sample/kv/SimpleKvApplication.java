package com.coohua.app.sample.kv;

import com.coohua.app.sample.kv.service.RedisCacheDemoService;
import com.coohua.app.sample.kv.service.RedisClusterCacheDemoService;
import com.coohua.app.sample.kv.service.SingleRedisSourceDemoService;
import com.coohua.caf.core.kv.EnableJedisClient;
import com.coohua.caf.core.kv.EnableJedisClusterClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.stereotype.Component;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * Created by zhangrongbin on 2018/09/27.
 */
@SpringBootApplication
@EnableJedisClient
//@EnableJedisClient(namespace = "api-limit")
@EnableJedisClusterClient
@Slf4j
public class SimpleKvApplication {
    public static final ExecutorService executor = Executors.newFixedThreadPool(10);
    public static void main(String[] args) {
        SpringApplication.run(SimpleKvApplication.class, args);
    }

    @Component
    class Runner implements ApplicationRunner {
        @Autowired
        private SingleRedisSourceDemoService singleRedisSourceDemoService;
//        @Autowired
        private RedisCacheDemoService redisCacheDemoService;
        @Autowired
        private RedisClusterCacheDemoService redisClusterCacheDemoService;
        @Override
        public void run(ApplicationArguments args) {
            redisClusterCacheDemoService.warmUp();
            singleRedisSourceDemoService.warmUp();
            executor.submit(() -> {
                for (;;) {
                    singleRedisSourceDemoService.helloRedis();
//                    singleRedisSourceDemoService.helloRedisCluster();

//                    redisCacheDemoService.helloCache();

                    redisClusterCacheDemoService.helloCache();
                    //redisClusterCacheDemoService.helloTransaction();

                    TimeUnit.SECONDS.sleep(1);
                }
            });
        }
    }
}
