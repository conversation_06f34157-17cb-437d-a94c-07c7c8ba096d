server.port=8083
app.logging.path=logs/caf-sample-kv-simple

app.jedis.default.address=***************
app.jedis.default.port=6379
app.jedis.default.pool.max-total=300
app.jedis.default.pool.max-idle=15
app.jedis.default.pool.min-idle=15
app.jedis.default.pool.max-wait-millis=6000
app.jedis.default.pool.test-on-borrow=false
app.jedis.default.pool.test-on-create=false
app.jedis.default.pool.test-on-return=true
app.jedis.default.pool.test-while-idle=true

app.jedis.api-limit.address=************
app.jedis.api-limit.port=6379

app.jedis-cluster.default.address=***************:9700,***************:9701,***************:9702,***************:9703,***************:9704,***************:9705
app.jedis-cluster.default.pool.max-total=300
app.jedis-cluster.default.pool.max-idle=12
app.jedis-cluster.default.pool.min-idle=10
app.jedis-cluster.default.pool.max-wait-millis=6000
spring.profiles.active=dev


app.monitor.log.delay=10
app.monitor.log.period=10