package com.coohua.app.sample.kv.service;

import com.coohua.caf.core.kv.JedisClient;
import com.coohua.caf.core.kv.JedisClusterClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

/**
 * Created by zhangrongbin on 2018/10/08.
 */
@Service
public class ComplexRedisSourceDemoService {
    private static final Logger logger = LoggerFactory.getLogger(ComplexRedisSourceDemoService.class);

    @Autowired
    @Qualifier("redis1JedisClient")
    private JedisClient jedisClient1;

    @Autowired
    @Qualifier("redis2JedisClient")
    private JedisClient jedisClient2;

    @Autowired
    @Qualifier("cluster1JedisClusterClient")
    private JedisClusterClient cluster1JedisClusterClient;

    @Autowired
    @Qualifier("cluster2JedisClusterClient")
    private JedisClusterClient cluster2JedisClusterClient;

    public String helloRedis1() {
        logger.info("SingleRedisSourceDemoService ... helloRedis1()");
        jedisClient1.set("hello", "jedis1");
        String helloString = "helloRedis1()! from jedis: [hello=" + jedisClient1.get("hello") + "]";
        logger.info(helloString);
        return helloString;
    }

    public String helloRedis2() {
        logger.info("SingleRedisSourceDemoService ... helloRedis2()");
        jedisClient2.set("hello", "jedis2");
        String helloString = "helloRedis2()! from jedis: [hello=" + jedisClient2.get("hello") + "]";
        logger.info(helloString);
        return helloString;
    }

    public String helloRedisCluster1() {
        logger.info("SingleRedisSourceDemoService ... helloRedisCluster1()");
        cluster1JedisClusterClient.set("hello", "jedisCluster1");
        String helloString = "helloRedisCluster1()! from jedisCluster: [hello=" +
                cluster1JedisClusterClient.get("hello") +  "]";
        logger.info(helloString);
        return helloString;
    }

    public String helloRedisCluster2() {
        logger.info("SingleRedisSourceDemoService ... helloRedisCluster2()");
        cluster2JedisClusterClient.set("hello", "jedisCluster2");
        String helloString = "helloRedisCluster2()! from jedisCluster: [hello=" +
                cluster2JedisClusterClient.get("hello") +  "]";
        logger.info(helloString);
        return helloString;
    }

}
