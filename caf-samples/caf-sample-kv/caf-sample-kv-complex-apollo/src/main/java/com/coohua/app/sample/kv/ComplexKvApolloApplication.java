package com.coohua.app.sample.kv;

import com.coohua.app.sample.kv.service.ComplexRedisSourceDemoService;
import com.coohua.caf.core.kv.EnableJedisClient;
import com.coohua.caf.core.kv.EnableJedisClusterClient;
import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;

import java.util.concurrent.TimeUnit;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

/**
 * Created by zhangrongbin on 2018/09/27.
 */
@SpringBootApplication
@EnableJedisClient(namespace = "redis1")
@EnableJedisClient(namespace = "redis2")
@EnableJedisClusterClient(namespace = "cluster1")
@EnableJedisClusterClient(namespace = "cluster2")
@EnableApolloConfig
public class ComplexKvApolloApplication {

	private static final Logger logger = LoggerFactory.getLogger(ComplexKvApolloApplication.class);

	public static void main(String[] args) {
		SpringApplication.run(ComplexKvApolloApplication.class, args);
	}

	@Component
	class Runner implements ApplicationRunner, ApplicationContextAware {
		private ApplicationContext applicationContext;

		@Override
		public void run(ApplicationArguments args) {
			while (true) {
				try {
					ComplexRedisSourceDemoService service = applicationContext
							.getBean(ComplexRedisSourceDemoService.class);
					service.helloRedis1();
					service.helloRedis2();

					service.helloRedisCluster1();
					service.helloRedisCluster2();

					TimeUnit.SECONDS.sleep(1);
				} catch (Exception e) {
					logger.error(e.getMessage(), e);
				}
			}
		}

		@Override
		public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
			this.applicationContext = applicationContext;
		}
	}
}
