(1).apollo配置中心，私有空间application需要配置：
app.logging.path = D:\logs\cafComplexRedisApolloServer
# redis
app.jedis.redis1.address = ************
app.jedis.redis1.port = 6379
app.jedis.redis2.address = ************
app.jedis.redis2.port = 6379
# redis cluster
app.jedis-cluster.cluster1.address = ************:9700,************:9701,************:9702
app.jedis-cluster.cluster2.address = ************:9700,************:9702

(2).样例运行程序需要增加启动参数:

-Denv=dev -Dapollo.cluster=default -Dapp.id=cafComplexRedisApolloServer -Dapollo.meta=http://apollo-dev-1:8080,http://apollo-dev-2:8081,http://apollo-dev-3:8082