server.port=8083
spring.profiles.active=dev

app.logging.path=logs/caf-sample-kv-complex




# redis
app.jedis.redis1.address=************
app.jedis.redis1.port=6379
app.jedis.redis1.pool.max-total=301
app.jedis.redis1.pool.max-idle=11
app.jedis.redis1.pool.min-idle=1
app.jedis.redis1.pool.max-wait-millis=6001

app.jedis.redis2.address=************
app.jedis.redis2.port=6379
app.jedis.redis2.pool.max-total=302
app.jedis.redis2.pool.max-idle=12
app.jedis.redis2.pool.min-idle=2
app.jedis.redis2.pool.max-wait-millis=6002

# redis cluster
app.jedis-cluster.cluster1.address=************:9700,************:9701,************:9702
app.jedis-cluster.cluster1.pool.max-total=303
app.jedis-cluster.cluster1.pool.max-idle=13
app.jedis-cluster.cluster1.pool.min-idle=3
app.jedis-cluster.cluster1.pool.max-wait-millis=6003

app.jedis-cluster.cluster2.address=************:9700,************:9702
app.jedis-cluster.cluster2.pool.max-total=304
app.jedis-cluster.cluster2.pool.max-idle=14
app.jedis-cluster.cluster2.pool.min-idle=4
app.jedis-cluster.cluster2.pool.max-wait-millis=6004

app.monitor.log.delay=10
app.monitor.log.period=10
