package com.coohua.app.sample.kv;

import com.coohua.app.sample.kv.service.ComplexRedisSourceDemoService;
import com.coohua.caf.core.kv.EnableJedisClient;
import com.coohua.caf.core.kv.EnableJedisClusterClient;
import org.springframework.beans.BeansException;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * Created by zhang<PERSON>bin on 2018/09/27.
 */
@SpringBootApplication
@EnableJedisClient(namespace = "redis1")
@EnableJedisClient(namespace = "redis2")
@EnableJedisClusterClient(namespace = "cluster1")
@EnableJedisClusterClient(namespace = "cluster2")
public class ComplexKvApplication {
    public static final ExecutorService executor = Executors.newFixedThreadPool(10);
    public static void main(String[] args) {
        SpringApplication.run(ComplexKvApplication.class, args);
    }

    @Component
    class Runner implements ApplicationRunner, ApplicationContextAware {
        private ApplicationContext applicationContext;
        @Override
        public void run(ApplicationArguments args) {
            ComplexRedisSourceDemoService service = applicationContext.getBean(ComplexRedisSourceDemoService.class);
            executor.submit(() -> {
                for (;;) {
                    service.helloRedis1();
                    service.helloRedisCluster1();
                    try {
                        TimeUnit.MILLISECONDS.sleep(100);
                    } catch (InterruptedException e) {

                    }
                }
            });
            executor.submit(() -> {
                for (;;) {
                    service.helloRedis2();
                    service.helloRedisCluster2();
                    try {
                        TimeUnit.MILLISECONDS.sleep(50);
                    } catch (InterruptedException e) {

                    }
                }
            });
        }

        @Override
        public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
            this.applicationContext = applicationContext;
        }
    }
}
