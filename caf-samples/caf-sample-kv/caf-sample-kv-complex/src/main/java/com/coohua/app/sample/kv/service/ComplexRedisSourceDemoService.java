package com.coohua.app.sample.kv.service;

import com.coohua.caf.core.kv.JedisClient;
import com.coohua.caf.core.kv.JedisClusterClient;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2018/10/08.
 */
@Service
@Slf4j
public class ComplexRedisSourceDemoService {

    @Autowired
    private JedisClient redis1JedisClient;

    /**
     * 保证bean名相同或者使用@Qualifier关键字都可以进行bean绑定
     */
    @Autowired
    @Qualifier("redis2JedisClient")
    private JedisClient jedisClient2;

    @Autowired
    private JedisClusterClient cluster1JedisClusterClient;

    @Autowired
    private JedisClusterClient cluster2JedisClusterClient;

    public String helloRedis1() {
        log.info("SingleRedisSourceDemoService ... helloRedis1()");
        redis1JedisClient.set("hello", "jedis1");
        String helloString = "helloRedis1()! from jedis: [hello=" + redis1JedisClient.get("hello") + "]";
        log.info(helloString);
        return helloString;
    }

    public String helloRedis2() {
        log.info("SingleRedisSourceDemoService ... helloRedis2()");
        jedisClient2.set("hello", "jedis2");
        String helloString = "helloRedis2()! from jedis: [hello=" + jedisClient2.get("hello") + "]";
        log.info(helloString);
        return helloString;
    }

    public String helloRedisCluster1() {
        log.info("SingleRedisSourceDemoService ... helloRedisCluster1()");
        cluster1JedisClusterClient.set("hello", "jedisCluster1");
        String helloString = "helloRedisCluster1()! from jedisCluster: [hello=" +
                cluster1JedisClusterClient.get("hello") +  "]";
        log.info(helloString);
        return helloString;
    }

    public String helloRedisCluster2() {
        log.info("SingleRedisSourceDemoService ... helloRedisCluster2()");
        cluster2JedisClusterClient.set("hello", "jedisCluster2");
        String helloString = "helloRedisCluster2()! from jedisCluster: [hello=" +
                cluster2JedisClusterClient.get("hello") +  "]";
        log.info(helloString);
        return helloString;
    }

}
