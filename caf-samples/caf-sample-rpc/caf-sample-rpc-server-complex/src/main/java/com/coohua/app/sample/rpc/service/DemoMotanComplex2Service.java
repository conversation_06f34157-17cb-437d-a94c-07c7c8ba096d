package com.coohua.app.sample.rpc.service;

import com.coohua.app.sample.rpc.api.IDemoMotanComplexService;
import com.weibo.api.motan.config.springsupport.annotation.MotanService;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Created by zhangrongbin on 2018/09/27.
 */
@MotanService(basicService = "motan2BasicServiceConfigBean")
@Slf4j
public class DemoMotanComplex2Service implements IDemoMotanComplexService {
    private ScheduledExecutorService executorService = Executors.newScheduledThreadPool(1);
    private AtomicInteger counter = new AtomicInteger();
    public String hello() {
        log.info("DemoMotanComplex2Service ... hello()");
        if (counter.incrementAndGet() > 10) {
//            executorService.schedule(() -> System.exit(0), 10, TimeUnit.SECONDS);
        }
        return "DemoMotanComplex2Service hello robin!";
    }
}
