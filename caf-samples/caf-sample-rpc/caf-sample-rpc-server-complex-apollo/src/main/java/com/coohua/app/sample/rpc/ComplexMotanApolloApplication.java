package com.coohua.app.sample.rpc;

import com.coohua.caf.core.rpc.EnableMotan;
import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2018/09/27.
 */
@SpringBootApplication
@EnableMotan(namespace = "motan1")
@EnableMotan(namespace = "motan2")
@EnableMotan(namespace = "motan3")
@EnableMotan(namespace = "motan4")
@EnableApolloConfig
public class ComplexMotanApolloApplication {
	public static void main(String[] args) {
		SpringApplication.run(ComplexMotanApolloApplication.class, args);
	}
}
