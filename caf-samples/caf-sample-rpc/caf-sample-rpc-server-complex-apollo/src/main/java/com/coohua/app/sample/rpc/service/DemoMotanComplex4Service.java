package com.coohua.app.sample.rpc.service;

import com.coohua.app.sample.rpc.api.IDemoMotanComplexService;
import com.weibo.api.motan.config.springsupport.annotation.MotanService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2018/09/27.
 */
@MotanService(basicService = "motan4BasicServiceConfigBean")
public class DemoMotanComplex4Service implements IDemoMotanComplexService {
    private ScheduledExecutorService executorService = Executors.newScheduledThreadPool(1);
    private AtomicInteger counter = new AtomicInteger();
    private static final Logger logger = LoggerFactory.getLogger(DemoMotanComplex4Service.class);
    public String hello() {
        logger.info("DemoMotanComplex2Service ... hello()");
        if (counter.incrementAndGet() > 2) {
            executorService.schedule(() -> System.exit(0), 10, TimeUnit.SECONDS);
        }
        return "hello robin!";
    }
}
