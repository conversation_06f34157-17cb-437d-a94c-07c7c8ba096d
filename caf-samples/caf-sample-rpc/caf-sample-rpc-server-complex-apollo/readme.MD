(1).apollo配置中心，私有空间application需要配置：
app.logging.path = D:\logs\cafComplexRpcApolloServer
app.motan.motan1.port = 1234
app.motan.motan1.registry.address = ************:2181
app.motan.motan2.port = 1235
app.motan.motan2.registry.address = ************:2181
app.motan.motan3.port = 1236
app.motan.motan3.registry.address = ************:2181
app.motan.motan4.port = 1237
app.motan.motan4.registry.address = ************:2181

(2).样例运行程序需要增加启动参数:

-Denv=dev -Dapollo.cluster=default -Dapp.id=cafComplexRpcApolloServer -Dapollo.meta=http://apollo-dev-1:8080,http://apollo-dev-2:8081,http://apollo-dev-3:8082