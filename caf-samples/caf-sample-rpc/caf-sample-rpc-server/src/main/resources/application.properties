app.logging.path=logs/caf-sample-rpc-server
app.motan.default.annotation.package=com.coohua.app.sample.rpc.service
app.motan.registry.address=127.0.0.1:2181
app.motan.sentinel.enable=true
#app.motan.default.port=10000
#app.motan.default.protocol.min-worker-thread=21
#app.motan.default.protocol.max-worker-thread=41
#app.motan.default.protocol.ha-strategy=failover
#app.motan.default.protocol.name=motan
#app.motan.default.protocol.request-timeout=3000
#app.motan.default.protocol.is-default=true
#app.motan.default.protocol.retries=2
#app.motan.default.protocol.loadbalance=activeWeight
#app.motan.default.protocol.cluster=default

#app.motan.registry.check=false
#app.motan.registry.connect-timeout=1000
#app.motan.registry.request-timeout=3001
#app.motan.registry.registry-session-timeout=60
#app.motan.registry.registry-retry-period=30
#app.motan.registry.name=zookeeper
#app.motan.registry.is-default=true
#app.motan.registry.register=true
#app.motan.registry.subscribe=true
#app.motan.default.registry.address=************:2181
#app.motan.default.registry.check=false
#app.motan.default.registry.connect-timeout=1000
#app.motan.default.registry.request-timeout=200
#app.motan.default.registry.registry-session-timeout=60
#app.motan.default.registry.registry-retry-period=30
#app.motan.default.registry.name=zookeeper
#app.motan.default.registry.is-default=true
#app.motan.default.registry.register=true
#app.motan.default.registry.subscribe=true

#app.motan.default.basic-referer.actives=0
#app.motan.default.basic-referer.application=rpc-client
#app.motan.default.basic-referer.group=rpc
#app.motan.default.basic-referer.check=false
#app.motan.default.basic-referer.module=client
#app.motan.default.basic-referer.request-timeout=3002
#app.motan.default.basic-referer.retries=3
#app.motan.default.basic-referer.version=1.0.0
#
#app.motan.default.basic-service.actives=0
#app.motan.default.basic-service.application=rpc-server
#app.motan.default.basic-service.group=rpc
#app.motan.default.basic-service.check=false
#app.motan.default.basic-service.module=server
#app.motan.default.basic-service.request-timeout=3003
#app.motan.default.basic-service.retries=3
#app.motan.default.basic-service.version=1.0.0

spring.profiles.active=dev
