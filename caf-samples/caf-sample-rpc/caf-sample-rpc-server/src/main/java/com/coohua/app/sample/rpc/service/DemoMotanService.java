package com.coohua.app.sample.rpc.service;

import com.alibaba.csp.sentinel.annotation.SentinelResource;
import com.alibaba.csp.sentinel.slots.block.BlockException;
import com.alibaba.csp.sentinel.slots.block.degrade.DegradeException;
import com.coohua.app.sample.rpc.api.IDemoMotanService;
import com.coohua.caf.core.metrics.Profile;
import com.weibo.api.motan.config.springsupport.annotation.MotanService;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Created by zhangrongbin on 2018/09/27.
 */
@MotanService
@Slf4j
public class DemoMotanService implements IDemoMotanService {
    private ScheduledExecutorService executorService = Executors.newScheduledThreadPool(1);
    private AtomicInteger counter = new AtomicInteger();
    @SentinelResource(value = "hello", blockHandler = "internalBlock")
    public String hello() {
//        log.info("DemoMotanService ... hello()");
//        if (counter.incrementAndGet() > 2) {
//            executorService.schedule(() -> System.exit(0), 10, TimeUnit.SECONDS);
//        }
        sleep10mms();
        return "hello robin!";
    }

    public String internalBlock(BlockException ex) {
        if (ex instanceof DegradeException) {
            return "internalFallback!!!";
        }
        return "internalBlock!!!";
    }

    public String helloBlock() {
        sleep10mms();
        return "hello blocked!";
    }

    public String helloFallback() {
        sleep10mms();
        return "hello fallback!";
    }

    @Profile
    private void sleep10mms() {
        try {
            TimeUnit.MILLISECONDS.sleep(10);
        } catch (InterruptedException ignored) {

        }
    }
}
