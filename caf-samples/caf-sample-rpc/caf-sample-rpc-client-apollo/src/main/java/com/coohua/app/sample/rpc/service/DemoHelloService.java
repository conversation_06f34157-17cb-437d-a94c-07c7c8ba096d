package com.coohua.app.sample.rpc.service;

import com.coohua.app.sample.rpc.api.IDemoMotanService;
import com.weibo.api.motan.config.springsupport.annotation.MotanReferer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2018/09/27.
 */
@Component
public class DemoHelloService {
    private static final Logger logger = LoggerFactory.getLogger(DemoHelloService.class);
    @MotanReferer
    private IDemoMotanService demoMontanService;

    public String helloMotan() {
        String hello = demoMontanService.hello();
        logger.info("DemoHelloService ... helloMotan() ... hello!...result->[" + hello + "]");
        return hello;
    }
}
