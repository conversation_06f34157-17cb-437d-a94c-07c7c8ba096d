package com.coohua.app.sample.rpc;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.stereotype.Component;

import com.coohua.app.sample.rpc.api.IDemoMotanComplexService;
import com.coohua.app.sample.rpc.api.IDemoMotanService;
import com.coohua.caf.core.rpc.EnableMotan;
import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import com.weibo.api.motan.config.springsupport.annotation.MotanReferer;

/**
 * Created by zhangrongbin on 2018/09/27.
 */
@SpringBootApplication
@EnableMotan
@EnableApolloConfig
public class SimpleClientApolloApplication {
	
	private static final Logger logger = LoggerFactory.getLogger(SimpleClientApolloApplication.class);
	
	public static void main(String[] args) {
		SpringApplication.run(SimpleClientApolloApplication.class, args);
	}

	@Component
	class Runner implements ApplicationRunner {

		@MotanReferer
		private IDemoMotanService demoMontanService;

		@MotanReferer
		private IDemoMotanComplexService demoMotanComplexService;

		@Override
		public void run(ApplicationArguments args) {
			// testSimple();
			testComplex();
			
			testSimple();
			System.exit(0);
		}

		private void testComplex() {
			for (int i = 0; i < 10; i++) {
				String hello = demoMotanComplexService.hello();
				logger.info("testComplex() ... helloMotan() ... hello!...result->[" + hello + "]");
			}
		}

		private void testSimple() {
			String hello = demoMontanService.hello();
			logger.info("testSimple() ... helloMotan() ... hello!...result->[" + hello + "]");
		}
	}
}
