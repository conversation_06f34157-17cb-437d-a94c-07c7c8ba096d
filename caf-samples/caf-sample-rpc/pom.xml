<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>caf-boot-parent</artifactId>
        <groupId>com.coohua.caf</groupId>
        <version>2.0.12-SNAPSHOT</version>
        <relativePath>../../caf-project/caf-boot-parent/pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>caf-sample-rpc</artifactId>
    <packaging>pom</packaging>
    <modules>
        <module>caf-sample-rpc-api</module>
        <module>caf-sample-rpc-server</module>
		<module>caf-sample-rpc-server-apollo</module>
        <module>caf-sample-rpc-server-complex</module>
		<module>caf-sample-rpc-server-complex-apollo</module>
        <module>caf-sample-rpc-client</module>
		<module>caf-sample-rpc-client-apollo</module>
    </modules>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.coohua.caf</groupId>
                <artifactId>caf-sample-rpc-api</artifactId>
                <version>2.0.12-SNAPSHOT</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
</project>