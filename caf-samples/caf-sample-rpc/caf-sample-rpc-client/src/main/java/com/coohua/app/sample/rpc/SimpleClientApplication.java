package com.coohua.app.sample.rpc;

import com.coohua.app.sample.rpc.api.IDemoMotanComplexService;
import com.coohua.app.sample.rpc.api.IDemoMotanService;
import com.coohua.caf.core.rpc.EnableMotan;
import com.weibo.api.motan.config.springsupport.annotation.MotanReferer;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.stereotype.Component;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * Created by zhangrongbin on 2018/09/27.
 */
@SpringBootApplication
@EnableMotan
@Slf4j
public class SimpleClientApplication {
    public static final ExecutorService EXECUTOR = Executors.newFixedThreadPool(10);
    public static void main(String[] args) {
        SpringApplication.run(SimpleClientApplication.class, args);
    }

    @Component
    class Runner implements ApplicationRunner {

        @MotanReferer
        private IDemoMotanService demoMontanService;

        @MotanReferer
        private IDemoMotanComplexService demoMotanComplexService;

        @Override
        public void run(ApplicationArguments args) {
            for (int i = 0; i < 20; i++) {
                EXECUTOR.submit(() -> {
                    for (;;) {
                        try {
                            testSimple();
                        } catch (Exception ignored) {
                            log.error("", ignored);
                        } finally {
                            TimeUnit.MILLISECONDS.sleep(500);
                        }
                    }
                });
            }

        }

        private void testSimple() {
            String hello = demoMontanService.hello();
            log.info("{} call {} result {}", "testSimple()", "demoMontanService.hello()", hello);
        }
    }
}
