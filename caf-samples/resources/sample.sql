CREATE TABLE `shop` (
  `id` bigint NOT NULL,
  `name` varchar(255) DEFAULT NULL,
  `owner_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

apollo.dev:
insert into shop values(1, "dev.malldb.shop", 1);
apollo.staging:
insert into shop values(1, "staging.malldb.shop", 1);
apollo.product:
insert into shop values(1, "product.malldb.shop", 1);

CREATE TABLE `user` (
  `id` int(11) NOT NULL,
  `name` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

apollo.dev:
insert into `user` values(1, "dev.userdb.user", 1);
apollo.staging:
insert into `user` values(1, "staging.userdb.user", 1);
apollo.product:
insert into `user` values(1, "product.userdb.user", 1);