package com.coohua.app.sample.logging;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * Created by z<PERSON><PERSON><PERSON> on 2018/09/27.
 */
@SpringBootApplication
public class SimpleLoggingApplication {
    public static void main(String[] args) {
        SpringApplication.run(SimpleLoggingApplication.class, args);
    }

    @Component
    class Runner implements ApplicationRunner, ApplicationContextAware {

        private ApplicationContext applicationContext;
        private final String String1K = StringUtils.repeat("K", 1024);
        private Logger appLogger = LoggerFactory.getLogger(SimpleLoggingApplication.class);
        private Logger frameworkLogger = LoggerFactory.getLogger("framework");
        private Logger traceLogger = LoggerFactory.getLogger("trace");
        private Logger errorLogger = LoggerFactory.getLogger("error");
        private Logger accessLogger = LoggerFactory.getLogger("access");
        private Logger performanceLogger = LoggerFactory.getLogger("performance");

        @Override
        public void run(ApplicationArguments args) {
            //for (int i = 0; i < 1; i++) {
            for (;;) {
                log1kString();
                try {
                    TimeUnit.MILLISECONDS.sleep(10);
                } catch (InterruptedException ignored) {
                }
            }
        }

        private void log1kString() {
            errorLogger.error(String1K);
            appLogger.info(String1K);
            frameworkLogger.info(String1K);
            accessLogger.info(String1K);
            performanceLogger.info(String1K);
            traceLogger.info(String1K);
        }

        @Override
        public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
            this.applicationContext = applicationContext;
        }
    }
}
