package com.coohua.app.sample.web.httpclient;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;

@Slf4j
public class HttpClientNioRunner implements ApplicationRunner, InitializingBean {
    @Override
    public void afterPropertiesSet() throws Exception {

    }

    @Override
    public void run(ApplicationArguments args) throws Exception {

    }
//    private CloseableHttpAsyncClient httpclient;
//
//    private final ExecutorService executor = Executors.newFixedThreadPool(100);
//    @Override
//    public void run(ApplicationArguments args) {
//        int loop = 10;
//        for (int i = 0; i < loop; i++) {
//            String REQ_ID = UUID.randomUUID().toString();
//            long begin = System.currentTimeMillis();
//            log.info("start {} at: {}", REQ_ID, DateTime.now());
//            Future<HttpResponse> responseFuture = httpclient.execute(new HttpGet("http://localhost:8083/service/latency/1000/bytes/100"), new FutureCallback<HttpResponse>() {
//                @Override
//                public void completed(HttpResponse httpResponse) {
//                    log.info(
//                            "finish {} at: {} with status code: {}, cost: {}ms",
//                            REQ_ID,
//                            DateTime.now(),
//                            httpResponse != null ? httpResponse.getStatusLine().getStatusCode() : -1,
//                            System.currentTimeMillis() - begin
//                    );
//                }
//
//                @Override
//                public void failed(Exception ex) {
//                    log.info("failed " + REQ_ID, ex);
//                }
//
//                @Override
//                public void cancelled() {
//                }
//            });
//            try {
//                responseFuture.get(100, TimeUnit.MILLISECONDS);
//            } catch (InterruptedException | ExecutionException | TimeoutException e) {
//                log.error("", e);
//            }
////            executor.submit(new HttpTask(httpClient, System.currentTimeMillis(), REQ_ID));
//        }
//    }
//
//    @Override
//    public void afterPropertiesSet() throws IOReactorException {
//        RequestConfig defaultRequestConfig = RequestConfig.custom()
////                .setConnectTimeout(timeout)
////                .setSocketTimeout(timeout)
//                .setConnectionRequestTimeout(100)
//                .build();
//        ConnectingIOReactor ioReactor = new DefaultConnectingIOReactor();
//        PoolingNHttpClientConnectionManager cm = new PoolingNHttpClientConnectionManager(ioReactor);
//        cm.setDefaultMaxPerRoute(5);
//        cm.setMaxTotal(100);
//        httpclient = HttpAsyncClients.custom()
//                .setConnectionManager(cm)
//                .setDefaultRequestConfig(defaultRequestConfig)
//                .build();
//        httpclient.start();
////        IdleConnectionEvictor connEvictor = new IdleConnectionEvictor(cm, 60, TimeUnit.SECONDS);
////        connEvictor.start();
//
//    }
}