package com.coohua.app.sample.web.controller;

import com.coohua.app.sample.web.service.HelloService;
import com.coohua.caf.core.metrics.Profile;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 19-8-19
 */
@RestController
@RequestMapping(value = "/simple")
public class SimpleController {
    @Autowired
    private HelloService helloService;
    @RequestMapping("/hello")
    public String hello() {
        return ((SimpleController)AopContext.currentProxy()).internalHello();
    }

    @Profile
    public String internalHello() {
        return helloService.hello();
    }
}
