package com.coohua.app.sample.web.httpclient;

import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.joda.time.DateTime;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;

import java.io.IOException;
import java.util.UUID;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Slf4j
public class HttpClientBioRunner implements ApplicationRunner {
    PoolingHttpClientConnectionManager cm = new PoolingHttpClientConnectionManager();
    RequestConfig defaultRequestConfig = RequestConfig.custom()
//                .setConnectTimeout(timeout)
//                .setSocketTimeout(timeout)
            .setConnectionRequestTimeout(100)
            .build();
    CloseableHttpClient httpClient = HttpClients.custom()
            .setConnectionManager(cm)
            .setDefaultRequestConfig(defaultRequestConfig)
            .build();

    private final ExecutorService executor = Executors.newFixedThreadPool(100);
    @Override
    public void run(ApplicationArguments args) {
        cm.setDefaultMaxPerRoute(2);
        cm.setMaxTotal(100);
        int loop = 10;
        for (int i = 0; i < loop; i++) {
            String REQ_ID = UUID.randomUUID().toString();
            log.info("start {} at: {}", REQ_ID, DateTime.now());
            executor.submit(new HttpTask(httpClient, System.currentTimeMillis(), REQ_ID));
        }
    }


    class HttpTask implements Runnable {
        CloseableHttpClient httpClient;
        long begin;
        String REQ_ID;

        public HttpTask(CloseableHttpClient httpClient, long begin, String REQ_ID) {
            this.httpClient = httpClient;
            this.begin = begin;
            this.REQ_ID = REQ_ID;
        }

        @Override
        public void run() {
            CloseableHttpResponse httpResponse = null;
            try {
                httpResponse = httpClient.execute(new HttpGet("http://localhost:8083/service/latency/1000/bytes/100"));
            } catch (IOException e) {
                log.error("", e);
            } finally {
                log.info(
                        "finish {} at: {} with status code: {}, cost: {}ms",
                        REQ_ID,
                        DateTime.now(),
                        httpResponse != null ? httpResponse.getStatusLine().getStatusCode() : -1,
                        System.currentTimeMillis() - begin
                );
                try {
                    if (httpResponse != null)
                        httpResponse.close();
                } catch (IOException ignored) {

                }
            }
        }
    }
}