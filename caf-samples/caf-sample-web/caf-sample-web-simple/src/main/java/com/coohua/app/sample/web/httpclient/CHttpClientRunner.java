package com.coohua.app.sample.web.httpclient;

import com.alibaba.csp.sentinel.slots.block.BlockException;
import com.coohua.caf.core.web.CHttpClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomUtils;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import java.util.UUID;
import java.util.concurrent.*;

@Slf4j
//@Component
public class CHttpClientRunner implements ApplicationRunner {
    @Autowired
    private CHttpClient cHttpClient;

    private static final ExecutorService EXECUTOR = Executors.newFixedThreadPool(2);
    private static final ExecutorService MAIN = Executors.newSingleThreadExecutor();

    @Override
    public void run(ApplicationArguments args) {
        MAIN.submit(() -> {
            for (;;) {
                EXECUTOR.execute(() -> {
                    String REQ_ID = UUID.randomUUID().toString();
                    HttpGet request = new HttpGet("http://localhost:8083/service/latency/100/bytes/100");

                    // mock random spike
                    if (RandomUtils.nextInt(0, 100) > 90) {
                        request = new HttpGet("http://localhost:8083/service/latency/500/bytes/100");
                    }
                    HttpResponse httpResponse = null;
                    try {
                        httpResponse = cHttpClient.execute(REQ_ID, request, 1000, TimeUnit.MILLISECONDS);
                    } catch (InterruptedException | ExecutionException | TimeoutException | BlockException ignored) {
                    }
                });
                try {
                    TimeUnit.MILLISECONDS.sleep(5000);
                } catch (InterruptedException e) {

                }
//                EXECUTOR.submit(() -> {
//                    String REQ_ID = UUID.randomUUID().toString();
//                    HttpGet request = new HttpGet("http://localhost:8083/service/latency/100/bytes/100");
//                    HttpResponse httpResponse = null;
//                    try {
//                        httpResponse = cHttpClient.execute(REQ_ID, request, 1000, TimeUnit.MILLISECONDS);
//                    } catch (InterruptedException | ExecutionException | TimeoutException ignored) {
//                    }
//                    try {
//                        TimeUnit.MILLISECONDS.sleep(1000);
//                    } catch (InterruptedException e) {
//
//                    }
//                });
            }
        });
    }
}
