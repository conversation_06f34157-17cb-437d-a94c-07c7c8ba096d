server.port=8080
server.tomcat.max-connections=5000
server.tomcat.max-threads=500
app.logging.path=logs/caf-sample-web-simple
#app.logging.level=debug
app.monitor.prometheus.port=9145
app.monitor.profile.http.enable=true


management.health.defaults.enabled=false
management.endpoint.health.show-details=always
management.endpoints.web.exposure.include=info, health, prometheus
management.info.build.enabled=true
management.info.env.enabled=true
management.info.git.enabled=true
management.info.git.mode=full
management.info.defaults.enabled=true
#è¿æ¥æ± è·åè¿æ¥ç­å¾è¶æ¶æ¶é´
app.chttpclient.default.props.connection-request-timeout=500
#socketè¶æ¶æ¶é´
app.chttpclient.default.props.socket-timeout=500
#socketå»ºç«è¿æ¥è¶æ¶æ¶é´
app.chttpclient.default.props.connect-timeout=500
#è¿æ¥æ± ä¸­æ¯ä¸ªrouteçè¿æ¥æ°
app.chttpclient.default.props.default-max-per-route=50
#è¿æ¥æ± æ»è¿æ¥æ°
app.chttpclient.default.props.max-total=500
#management.endpoints.web.exposure.include=info, health, metrics, prometheus
#management.endpoint.metrics.enabled=true
#management.metrics.export.simple.enabled=true
#management.metrics.export.prometheus.enabled=true
#management.metrics.use-global-registry=false