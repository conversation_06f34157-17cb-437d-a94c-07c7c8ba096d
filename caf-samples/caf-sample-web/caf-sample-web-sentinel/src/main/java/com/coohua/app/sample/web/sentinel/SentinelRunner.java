package com.coohua.app.sample.web.sentinel;

import com.alibaba.csp.sentinel.Constants;
import com.alibaba.csp.sentinel.Entry;
import com.alibaba.csp.sentinel.SphU;
import com.alibaba.csp.sentinel.command.vo.NodeVo;
import com.alibaba.csp.sentinel.node.ClusterNode;
import com.alibaba.csp.sentinel.node.DefaultNode;
import com.alibaba.csp.sentinel.node.Node;
import com.alibaba.csp.sentinel.slotchain.ResourceWrapper;
import com.alibaba.csp.sentinel.slots.block.BlockException;
import com.alibaba.csp.sentinel.slots.clusterbuilder.ClusterBuilderSlot;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomUtils;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class SentinelRunner implements ApplicationRunner {

    private static final ExecutorService EXECUTOR = Executors.newFixedThreadPool(20);
    private static final ExecutorService MAIN = Executors.newSingleThreadExecutor();
    private static final ScheduledExecutorService SCHEDULE = Executors.newSingleThreadScheduledExecutor();

    @Override
    public void run(ApplicationArguments args) {
//        SCHEDULE.scheduleAtFixedRate(() -> {
//            clusterNode();
//            jsonTree();
//        }, 10, 10, TimeUnit.SECONDS);

        MAIN.execute(() -> {
            for (;;) {
                Entry entry = null;
                try {
                    entry = SphU.entry("main");
                    EXECUTOR.submit(() -> {
                        int latency = RandomUtils.nextInt(90, 120);
                        latency(latency, TimeUnit.MILLISECONDS);
                    });
                } catch (BlockException ignored) {
                } finally {
                    if (entry != null) {
                        entry.exit();
                    }
                }
                try {
                    TimeUnit.MILLISECONDS.sleep(50);
                } catch (InterruptedException ignored) {

                }
            }
        });
    }

    private void jsonTree() {
        List<NodeVo> results = new ArrayList<NodeVo>();
        visit(Constants.ROOT, results, null);

        log.info(JSON.toJSONString(results, true));
    }

    private void clusterNode() {
        /*
         * type==notZero means nodes whose totalRequest <= 0 will be ignored.
         */
        List<NodeVo> list = new ArrayList<>();
        Map<ResourceWrapper, ClusterNode> map = ClusterBuilderSlot.getClusterNodeMap();
        if (map == null) {
            return;
        }
        for (Map.Entry<ResourceWrapper, ClusterNode> entry : map.entrySet()) {
            list.add(NodeVo.fromClusterNode(entry.getKey(), entry.getValue()));
        }
        log.info(JSON.toJSONString(list, true));
    }

    private void latency(int latency, TimeUnit timeUnit) {
        Entry entry = null;
        try {
            entry = SphU.entry("latency");
            try {
                timeUnit.sleep(latency);
            } catch (InterruptedException ignored) {

            }
        } catch (BlockException ignored) {
        } finally {
            if (entry != null) {
                entry.exit();
            }
        }

    }

    /**
     * Preorder traversal.
     */
    private void visit(DefaultNode node, List<NodeVo> results, String parentId) {
        NodeVo vo = NodeVo.fromDefaultNode(node, parentId);
        results.add(vo);
        String id = vo.getId();
        for (Node n : node.getChildList()) {
            visit((DefaultNode)n, results, id);
        }
    }
}
