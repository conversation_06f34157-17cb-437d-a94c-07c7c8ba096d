server.port=8080
server.tomcat.max-connections=5000
server.tomcat.max-threads=500
app.logging.path=logs/caf-sample-web-simple
#app.logging.level=debug
app.monitor.prometheus.port=9145
app.monitor.profile.http.enable=true


management.health.defaults.enabled=false
management.endpoint.health.show-details=always
management.endpoints.web.exposure.include=info, health, prometheus
management.info.build.enabled=true
management.info.env.enabled=true
management.info.git.enabled=true
management.info.git.mode=full
management.info.defaults.enabled=true
app.chttpclient.default.props.connection-request-timeout=101
app.chttpclient.default.props.socket-timeout=2001
app.chttpclient.default.props.connect-timeout=2001
app.chttpclient.default.props.default-max-per-route=51
app.chttpclient.default.props.max-total=501
#management.endpoints.web.exposure.include=info, health, metrics, prometheus
#management.endpoint.metrics.enabled=true
#management.metrics.export.simple.enabled=true
#management.metrics.export.prometheus.enabled=true
#management.metrics.use-global-registry=false