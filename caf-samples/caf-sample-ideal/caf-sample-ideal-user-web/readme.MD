(1).apollo配置中心，私有空间application需要配置：
app.logging.path = D:\logs\caf-sample-allinone-web
app.motan.userrpc.annotation.package = com.coohua.allinone.remote.api
app.motan.userrpc.registry.address = ************:2181
app.motan.shoprpc.annotation.package = com.coohua.allinone.remote.api
app.motan.shoprpc.registry.address = ************:2181


(2).样例运行程序需要增加启动参数:

-Denv=dev -Dapollo.cluster=default -Dapp.id=caf-sample-allinone-web -Dapollo.meta=http://apollo-dev-1:8080,http://apollo-dev-2:8081,http://apollo-dev-3:8082