package com.coohua.user.web.config;

import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;

import lombok.extern.slf4j.Slf4j;

@Configuration
@Slf4j
public class BizConfiguration implements InitializingBean {

	@Bean
	public PayConfigBean getPayConfigBean() {
		return new PayConfigBean();
	}

	@Override
	public void afterPropertiesSet() throws Exception {
		log.info(JSON.toJSONString(getPayConfigBean(), SerializerFeature.PrettyFormat,
				SerializerFeature.WriteClassName));
	}

}
