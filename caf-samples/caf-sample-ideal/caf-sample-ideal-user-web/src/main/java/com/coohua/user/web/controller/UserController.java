package com.coohua.user.web.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.coohua.caf.core.base.WebResult;
import com.coohua.user.web.service.UserService;
import com.coohua.user.web.vo.UserDetailVO;
import com.coohua.user.web.vo.UserVO;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2018/09/27.
 */
@RestController
@RequestMapping(value = "/user")
public class UserController {

	@Autowired
	private UserService userSerivce;

	@RequestMapping("/getUserDetail")
	public WebResult<UserDetailVO> getUser(@RequestParam(name = "userId") Long userId) {
		UserDetailVO userDTO = userSerivce.getUserDetail(userId);
		return new WebResult<>(WebResult.CODE_SUCCESS, "getDetailUser success.", userDTO);
	}

	@RequestMapping("/getUser")
	public WebResult<UserVO> getUserDetail(@RequestParam(name = "userId") Long userId) {
		UserVO userDTO = userSerivce.getUser(userId);
		return new WebResult<>(WebResult.CODE_SUCCESS, "getUser success.", userDTO);
	}
}
