package com.coohua.user.web.vo;

import java.io.Serializable;
import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@ToString
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class UserDetailVO implements Serializable {

	private static final long serialVersionUID = -7428570121510849305L;

	private Long id;

	private String name;

	private List<ShopVO> shopList;
}
