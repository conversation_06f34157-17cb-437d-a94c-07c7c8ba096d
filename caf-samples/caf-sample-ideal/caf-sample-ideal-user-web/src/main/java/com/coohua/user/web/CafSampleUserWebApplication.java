package com.coohua.user.web;

import com.coohua.caf.core.apollo.EnableAutoChangeApolloConfig;
import com.coohua.caf.core.rpc.EnableMotan;
import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * Created by zhangrongbin on 2018/09/27.
 */
@SpringBootApplication
@EnableApolloConfig(value = { "application", "caf.rpc.referer.user", "caf.rpc.referer.mall", "caf.biz.pay" })
@EnableAutoChangeApolloConfig
@EnableMotan(namespace = "user")
@EnableMotan(namespace = "shop")
public class CafSampleUserWebApplication {

	public static void main(String[] args) {
		SpringApplication.run(CafSampleUserWebApplication.class, args);
	}

}
