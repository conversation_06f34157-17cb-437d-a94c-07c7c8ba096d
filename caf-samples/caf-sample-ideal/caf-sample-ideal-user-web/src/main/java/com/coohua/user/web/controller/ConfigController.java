package com.coohua.user.web.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.coohua.caf.core.base.WebResult;
import com.coohua.user.web.service.ConfigService;
import com.coohua.user.web.vo.ConfigVO;

@RestController
@RequestMapping(value = "/config")
public class ConfigController {

	@Autowired
	private ConfigService configService;

	@RequestMapping("/getPayConfig")
	public WebResult<ConfigVO> getConfig() {
		return new WebResult<>(WebResult.CODE_SUCCESS, "getConfig success.", configService.getConfig());
	}

}
