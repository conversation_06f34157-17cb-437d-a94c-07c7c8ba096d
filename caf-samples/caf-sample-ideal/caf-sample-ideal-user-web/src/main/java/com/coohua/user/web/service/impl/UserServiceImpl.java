package com.coohua.user.web.service.impl;

import java.util.ArrayList;
import java.util.List;

import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.coohua.mall.remote.api.IShopRPC;
import com.coohua.mall.remote.dto.ShopDTO;
import com.coohua.user.remote.api.IUserRPC;
import com.coohua.user.remote.dto.UserDTO;
import com.coohua.user.web.service.UserService;
import com.coohua.user.web.vo.ShopVO;
import com.coohua.user.web.vo.UserDetailVO;
import com.coohua.user.web.vo.UserVO;
import com.weibo.api.motan.config.springsupport.annotation.MotanReferer;

@Service
public class UserServiceImpl implements UserService {

	@MotanReferer(basicReferer = "shopBasicRefererConfigBean")
	private IShopRPC shopRPC;

	@MotanReferer(basicReferer = "userBasicRefererConfigBean")
	private IUserRPC userRPC;

	@Override
	public UserDetailVO getUserDetail(Long userId) {
		if (userId == null) {
			return null;
		}

		UserDTO userDTO = userRPC.getUser(userId);
		if (userDTO == null) {
			return null;
		}

		UserDetailVO userDetailVO = new UserDetailVO();
		userDetailVO.setId(userDTO.getId());
		userDetailVO.setName(userDTO.getName());

		List<ShopDTO> shopDTOList = shopRPC.findShopListByOwnerId(userId);
		if (!CollectionUtils.isEmpty(shopDTOList)) {
			List<ShopVO> shopVOList = new ArrayList<ShopVO>();
			ShopVO tvo = null;
			for (ShopDTO sm : shopDTOList) {
				tvo = new ShopVO();
				tvo.setId(sm.getId());
				tvo.setName(sm.getName());
				tvo.setAddress(sm.getAddress());
				shopVOList.add(tvo);
			}
			userDetailVO.setShopList(shopVOList);
		}

		return userDetailVO;
	}

	@Override
	public UserVO getUser(Long userId) {
		if (userId == null) {
			return null;
		}

		UserDTO userDTO = userRPC.getUser(userId);
		if (userDTO == null) {
			return null;
		}

		UserVO userVO = new UserVO();
		userVO.setId(userDTO.getId());
		userVO.setName(userDTO.getName());
		return userVO;
	}

}
