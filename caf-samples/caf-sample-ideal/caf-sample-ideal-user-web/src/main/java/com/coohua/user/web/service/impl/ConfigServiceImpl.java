package com.coohua.user.web.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.coohua.user.web.config.PayConfigBean;
import com.coohua.user.web.service.ConfigService;
import com.coohua.user.web.vo.ConfigVO;

@Service
public class ConfigServiceImpl implements ConfigService {

	@Autowired
	private PayConfigBean payConfigBean;

	@Override
	public ConfigVO getConfig() {
		ConfigVO configVO = new ConfigVO();
		if (payConfigBean != null) {
			configVO.setPayUrl(payConfigBean.getPayUrl());
		}
		return configVO;
	}

}
