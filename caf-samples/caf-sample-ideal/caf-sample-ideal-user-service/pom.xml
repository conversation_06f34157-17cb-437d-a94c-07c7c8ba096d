<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<parent>
		<artifactId>caf-sample-ideal</artifactId>
		<groupId>com.coohua.caf</groupId>
		<version>2.0.12-SNAPSHOT</version>
	</parent>
	<modelVersion>4.0.0</modelVersion>
	<artifactId>caf-sample-ideal-user-service</artifactId>

	<dependencies>
		<dependency>
			<groupId>com.coohua.caf</groupId>
			<artifactId>caf-boot-starter</artifactId>
		</dependency>
		<dependency>
			<groupId>com.coohua.caf</groupId>
			<artifactId>caf-sample-ideal-user-api</artifactId>
		</dependency>
	</dependencies>

	<build>
<!-- 		<plugins> -->
<!-- 			<plugin> -->
<!-- 				<groupId>org.springframework.boot</groupId> -->
<!-- 				<artifactId>spring-boot-maven-plugin</artifactId> -->
<!-- 				<configuration> -->
<!-- 					<mainClass>com.coohua.allinone.CafSampleAllinoneRPCApplication</mainClass> -->
<!-- 					<attach>false</attach> -->
<!-- 				</configuration> -->
<!-- 				<executions> -->
<!-- 					<execution> -->
<!-- 						<goals> -->
<!-- 							<goal>repackage</goal> -->
<!-- 						</goals> -->
<!-- 					</execution> -->
<!-- 				</executions> -->
<!-- 			</plugin> -->
<!-- 		</plugins> -->
		<pluginManagement>
			<plugins>
				<plugin>
					<groupId>org.eclipse.m2e</groupId>
					<artifactId>lifecycle-mapping</artifactId>
					<version>1.0.0</version>
					<configuration>
						<lifecycleMappingMetadata>
							<pluginExecutions>
								<pluginExecution>
									<pluginExecutionFilter>
										<groupId>org.codehaus.mojo</groupId>
										<artifactId>flatten-maven-plugin</artifactId>
										<versionRange>[1.0.0,)</versionRange>
										<goals>
											<goal>flatten</goal>
										</goals>
									</pluginExecutionFilter>
									<action>
										<ignore />
									</action>
								</pluginExecution>
							</pluginExecutions>
						</lifecycleMappingMetadata>
					</configuration>
				</plugin>
			</plugins>
		</pluginManagement>
	</build>
</project>