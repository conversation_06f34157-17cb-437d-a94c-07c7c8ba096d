package com.coohua.user.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.coohua.user.manager.UserManager;
import com.coohua.user.model.UserModel;
import com.coohua.user.remote.dto.UserDTO;
import com.coohua.user.service.UserService;

@Service
public class UserServiceImpl implements UserService {

	@Autowired
	private UserManager userManager;

	@Override
	public UserDTO getUser(Long userId) {
		if (userId == null) {
			return null;
		}

		UserModel userModel = userManager.findById(userId);

		UserDTO userDTO = null;
		if (userModel != null) {
			userDTO = new UserDTO();
			userDTO.setId(userModel.getId());
			userDTO.setName(userModel.getName());
		}

		return userDTO;
	}

}
