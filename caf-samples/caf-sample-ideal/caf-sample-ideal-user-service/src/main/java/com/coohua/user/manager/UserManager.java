package com.coohua.user.manager;

import com.alibaba.fastjson.TypeReference;
import com.coohua.caf.core.kv.JedisClusterClient;
import com.coohua.user.constant.KVKeyConstant;
import com.coohua.user.mapper.UserMapper;
import com.coohua.user.model.UserModel;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class UserManager {

	@Autowired
	private UserMapper userMapper;

	@Autowired
	private JedisClusterClient userJedisClusterClient;

	public UserModel findById(Long userId) {
		if (userId == null) {
			return null;
		}

		String key = String.format(KVKeyConstant.KEY_USER, userId);
		UserModel userModel = userJedisClusterClient.getCache(key, new TypeReference<UserModel>() {
		});

		if (userModel == null) {
			userModel = userMapper.findById(userId);
			if (userModel != null) {
				userJedisClusterClient.setCache(key, userModel);
			}
		}
		return userModel;
	}
}
