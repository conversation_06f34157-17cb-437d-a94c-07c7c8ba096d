package com.coohua.user;

import java.util.concurrent.TimeUnit;

import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import com.coohua.caf.core.apollo.EnableAutoChangeApolloConfig;
import com.coohua.caf.core.db.EnableDataSource;
import com.coohua.caf.core.kv.EnableJedisClusterClient;
import com.coohua.caf.core.rpc.EnableMotan;
import com.coohua.user.remote.dto.UserDTO;
import com.coohua.user.service.UserService;
import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;

import lombok.extern.slf4j.Slf4j;

/**
 * Created by zhangrongbin on 2018/09/27.
 */
@SpringBootApplication
//开启两个redis-cluster实例
@EnableJedisClusterClient(namespace = "user")
//开启两个datasource实例
@EnableDataSource(namespace = "user", mapperPackages = "com.coohua.user.mapper")
//开启端口
@EnableMotan(namespace = "user")
//开启apollo配置中心
@EnableApolloConfig(value = { "application", "caf.db.user", "caf.redis.cluster.rpc.user" })
//系统启动时会打印@EnableApolloConfig中指定的namespace的初始化值；并且如果运行时会打印发生变化的配置。
@EnableAutoChangeApolloConfig
@Slf4j
public class CafSampleUserRPCApplication {

	public static void main(String[] args) {
		SpringApplication.run(CafSampleUserRPCApplication.class, args);
	}

	@Component
	class Runner implements ApplicationRunner, ApplicationContextAware {
		private ApplicationContext applicationContext;

		@Autowired
		private UserService userService;

		@Override
		public void run(ApplicationArguments args) {
			while (true) {
				try {

					UserDTO userDTO = userService.getUser(1L);
					log.info("user:" + userDTO.toString());

					TimeUnit.SECONDS.sleep(3);
					// break;
				} catch (Exception e) {
					log.error(e.getMessage(), e);
				}
			}
		}

		@Override
		public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
			this.applicationContext = applicationContext;
		}
	}
}
