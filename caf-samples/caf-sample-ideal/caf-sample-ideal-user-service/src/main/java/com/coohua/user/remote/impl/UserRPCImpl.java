package com.coohua.user.remote.impl;

import org.springframework.beans.factory.annotation.Autowired;

import com.coohua.user.constant.KVKeyConstant;
import com.coohua.user.remote.api.IUserRPC;
import com.coohua.user.remote.dto.UserDTO;
import com.coohua.user.service.UserService;
import com.weibo.api.motan.config.springsupport.annotation.MotanService;

/**
 * Created by zhangrongbin on 2018/09/27.
 */
@MotanService(basicService = "userBasicServiceConfigBean")
public class UserRPCImpl implements IUserRPC {

	@Autowired
	private UserService userService;

	@Override
	public UserDTO getUser(Long userId) {
		// TODO 这里还有可能调用其他的RPC完成一些逻辑
		return userService.getUser(userId);
	}

	public static void main(String[] args) {
		System.out.println(String.format(KVKeyConstant.KEY_USER, 333));
	}
}
