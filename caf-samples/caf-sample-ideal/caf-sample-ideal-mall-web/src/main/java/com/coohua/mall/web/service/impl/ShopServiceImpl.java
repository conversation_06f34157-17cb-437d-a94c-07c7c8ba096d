package com.coohua.mall.web.service.impl;

import org.springframework.stereotype.Service;

import com.coohua.mall.remote.api.IShopRPC;
import com.coohua.mall.remote.dto.ShopDTO;
import com.coohua.mall.web.service.ShopService;
import com.coohua.mall.web.vo.ShopDetailVO;
import com.coohua.mall.web.vo.ShopVO;
import com.coohua.mall.web.vo.UserVO;
import com.coohua.user.remote.api.IUserRPC;
import com.coohua.user.remote.dto.UserDTO;
import com.weibo.api.motan.config.springsupport.annotation.MotanReferer;

@Service
public class ShopServiceImpl implements ShopService {

	@MotanReferer(basicReferer = "shopBasicRefererConfigBean")
	private IShopRPC shopRPC;

	@MotanReferer(basicReferer = "userBasicRefererConfigBean")
	private IUserRPC userRPC;

	@Override
	public ShopVO getShop(Long shopId) {
		if (shopId == null) {
			return null;
		}
		ShopDTO shopDTO = shopRPC.getShop(shopId);
		if (shopDTO == null) {
			return null;
		}
		ShopVO shopVO = new ShopVO();
		shopVO.setAddress(shopDTO.getAddress());
		shopVO.setId(shopDTO.getId());
		shopVO.setName(shopDTO.getName());
		shopVO.setOwnerId(shopDTO.getOwnerId());
		return shopVO;
	}

	@Override
	public ShopDetailVO getDetailShop(Long shopId) {
		if (shopId == null) {
			return null;
		}
		ShopDTO shopDTO = shopRPC.getShop(shopId);
		if (shopDTO == null) {
			return null;
		}
		ShopDetailVO shopDetailVO = new ShopDetailVO();
		shopDetailVO.setAddress(shopDTO.getAddress());
		shopDetailVO.setId(shopDTO.getId());
		shopDetailVO.setName(shopDTO.getName());
		shopDetailVO.setOwnerId(shopDTO.getOwnerId());

		if (shopDTO.getOwnerId() != null) {
			UserDTO userDTO = userRPC.getUser(shopDTO.getOwnerId());
			if (userDTO != null) {
				UserVO userVO = new UserVO();
				userVO.setId(shopDTO.getOwnerId());
				userVO.setName(userDTO.getName());
				shopDetailVO.setOwner(userVO);
			}
		}
		return shopDetailVO;
	}

}
