package com.coohua.mall.web.controller;

import com.coohua.mall.web.service.ShopService;
import com.coohua.mall.web.vo.ShopDetailVO;
import com.coohua.mall.web.vo.ShopVO;
import com.coohua.caf.core.base.WebResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/shop")
public class ShopController {

	@SuppressWarnings("unused")
	private static final Logger logger = LoggerFactory.getLogger(ShopController.class);

	@Autowired
	private ShopService shopService;

	@RequestMapping("/getShop")
	public WebResult<ShopVO> getShop(@RequestParam(name = "shopId") Long shopId) {
		ShopVO shopVO = shopService.getShop(shopId);
		return new WebResult<ShopVO>(WebResult.CODE_SUCCESS, "getShop success.", shopVO);
	}

	@RequestMapping("/getShopDetail")
	public WebResult<ShopDetailVO> getShopDetail(@RequestParam(name = "shopId") Long shopId) {
		ShopDetailVO shopDetailVO = shopService.getDetailShop(shopId);
		return new WebResult<>(WebResult.CODE_SUCCESS, "getShopDetail success.", shopDetailVO);
	}
}
