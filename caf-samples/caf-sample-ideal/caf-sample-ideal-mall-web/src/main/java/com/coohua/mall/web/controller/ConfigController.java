package com.coohua.mall.web.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.coohua.caf.core.base.WebResult;
import com.coohua.mall.web.service.ConfigService;
import com.coohua.mall.web.vo.ConfigVO;

@RestController
@RequestMapping(value = "/config")
public class ConfigController {

	@Autowired
	private ConfigService configService;

	@RequestMapping("/getConfig")
	public WebResult<ConfigVO> getPayConfig() {
		return new WebResult<>(WebResult.CODE_SUCCESS, "getConfig success.", configService.getConfig());
	}

}
