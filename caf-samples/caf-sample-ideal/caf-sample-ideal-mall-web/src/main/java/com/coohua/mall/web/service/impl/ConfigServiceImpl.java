package com.coohua.mall.web.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.coohua.mall.web.config.PayConfigBean;
import com.coohua.mall.web.config.SMSConfigBean;
import com.coohua.mall.web.config.SpideConfigBean;
import com.coohua.mall.web.service.ConfigService;
import com.coohua.mall.web.vo.ConfigVO;

@Service
public class ConfigServiceImpl implements ConfigService {

	@Autowired
	private PayConfigBean payConfigBean;

	@Autowired
	private SMSConfigBean smsConfigBean;

	@Autowired
	private SpideConfigBean spideConfigBean;

	@Override
	public ConfigVO getConfig() {
		ConfigVO configVO = new ConfigVO();
		if (payConfigBean != null) {
			configVO.setPayUrl(payConfigBean.getPayUrl());
		}
		if (smsConfigBean != null) {
			configVO.setAliyunSMSUrl(smsConfigBean.getAliyunSMSUrl());
			configVO.setMobileSMSUrl(smsConfigBean.getMobileSMSUrl());
			configVO.setUnicomSMSUrl(smsConfigBean.getUnicomSMSUrl());
		}
		if (spideConfigBean != null) {
			configVO.setToutiaoSpideUrl(spideConfigBean.getToutiaoSpideUrl());
			configVO.setQutoutiaoSpideUrl(spideConfigBean.getQutoutiaoSpideUrl());
		}
		return configVO;
	}

}
