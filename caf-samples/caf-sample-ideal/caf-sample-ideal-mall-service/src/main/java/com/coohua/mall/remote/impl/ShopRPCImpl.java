package com.coohua.mall.remote.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;

import com.coohua.mall.remote.api.IShopRPC;
import com.coohua.mall.remote.dto.ShopDTO;
import com.coohua.mall.service.ShopService;
import com.weibo.api.motan.config.springsupport.annotation.MotanService;

import lombok.extern.slf4j.Slf4j;

@MotanService(basicService = "mallBasicServiceConfigBean")
@Slf4j
public class ShopRPCImpl implements IShopRPC {

	@Autowired
	private ShopService shopService;

	@Override
	public ShopDTO getShop(Long shopId) {
		// TODO 这里还有可能调用其他的RPC完成一些逻辑，同时也有可能添加user关联信息，然后组装DTO。
		ShopDTO shop = shopService.getShop(shopId);
		log.info("{} - {} - {}", this.getClass(), "getShop()", shop);
		return shop;
	}

	@Override
	public List<ShopDTO> findShopListByOwnerId(Long userId) {
		return shopService.findShopListByOwnerId(userId);
	}

}
