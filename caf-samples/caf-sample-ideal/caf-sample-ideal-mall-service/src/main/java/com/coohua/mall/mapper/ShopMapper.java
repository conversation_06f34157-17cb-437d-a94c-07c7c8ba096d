package com.coohua.mall.mapper;

import org.apache.ibatis.annotations.Select;

import com.coohua.mall.model.ShopModel;

import java.util.List;

public interface ShopMapper {

	@Select("select id, name, owner_id, address from shop where id=#{shopId}")
	ShopModel findById(Long shopId);

	// 实际这里必须有数量限制
	@Select("select id, name, owner_id, address from shop where owner_id=#{ownerUserId} limit 10")
	List<ShopModel> shopModelList(Long ownerUserId);
}
