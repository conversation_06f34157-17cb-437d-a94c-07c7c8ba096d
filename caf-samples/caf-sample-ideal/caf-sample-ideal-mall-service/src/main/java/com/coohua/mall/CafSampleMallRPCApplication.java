package com.coohua.mall;

import java.util.List;
import java.util.concurrent.TimeUnit;

import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import com.coohua.caf.core.apollo.EnableAutoChangeApolloConfig;
import com.coohua.caf.core.db.EnableDataSource;
import com.coohua.caf.core.kv.EnableJedisClusterClient;
import com.coohua.caf.core.rpc.EnableMotan;
import com.coohua.mall.config.PayConfigBean;
import com.coohua.mall.config.SMSConfigBean;
import com.coohua.mall.config.SpideConfigBean;
import com.coohua.mall.remote.dto.ShopDTO;
import com.coohua.mall.service.ShopService;
import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;

import lombok.extern.slf4j.Slf4j;

/**
 * Created by zhangrongbin on 2018/09/27.
 */
@SpringBootApplication
//开启两个redis-cluster实例
@EnableJedisClusterClient(namespace = "mall")
//开启两个datasource实例
@EnableDataSource(namespace = "mall", mapperPackages = "com.coohua.mall.mapper")
//开启两个端口
@EnableMotan(namespace = "shop")
//开启apollo配置中心
@EnableApolloConfig(value = { "application", "caf.db.mall", "caf.redis.cluster.rpc.mall", "caf.biz.sms", "caf.biz.pay",
		"caf.biz.spide" })
//系统启动时会打印@EnableApolloConfig中指定的namespace的初始化值；并且如果运行时会打印发生变化的配置。
@EnableAutoChangeApolloConfig
@Slf4j
public class CafSampleMallRPCApplication {

	public static void main(String[] args) {
		SpringApplication.run(CafSampleMallRPCApplication.class, args);
	}

	@Component
	class Runner implements ApplicationRunner, ApplicationContextAware {
		private ApplicationContext applicationContext;

		@Autowired
		private ShopService shopService;

		@Autowired
		private PayConfigBean payConfigBean;

		@Autowired
		private SMSConfigBean smsConfigBean;

		@Autowired
		private SpideConfigBean spideConfigBean;

		@Override
		public void run(ApplicationArguments args) {
			while (true) {
				try {
					ShopDTO shopDTO = shopService.getShop(1L);
					log.info("shopDetail:" + shopDTO.toString());

					List<ShopDTO> shopDTOList = shopService.findShopListByOwnerId(1L);
					log.info("shopDTOList:" + shopDTOList.toString());

					log.info("payConfigBean:" + payConfigBean.toString());

					log.info("smsConfigBean:" + smsConfigBean.toString());

					log.info("spideConfigBean:" + spideConfigBean.toString());

					TimeUnit.SECONDS.sleep(3);
					// break;
				} catch (Exception e) {
					log.error(e.getMessage(), e);
				}
			}
		}

		@Override
		public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
			this.applicationContext = applicationContext;
		}
	}
}
