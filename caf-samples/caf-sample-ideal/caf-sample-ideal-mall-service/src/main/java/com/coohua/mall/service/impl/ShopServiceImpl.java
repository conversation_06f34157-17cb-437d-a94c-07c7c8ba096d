package com.coohua.mall.service.impl;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.coohua.mall.manager.ShopManager;
import com.coohua.mall.model.ShopModel;
import com.coohua.mall.remote.dto.ShopDTO;
import com.coohua.mall.service.ShopService;

@Service
public class ShopServiceImpl implements ShopService {

	@Autowired
	private ShopManager shopManager;

	@Override
	public ShopDTO getShop(Long shopId) {

		if (shopId == null) {
			return null;
		}

		ShopModel shopModel = shopManager.findById(shopId);

		ShopDTO shopDTO = null;
		if (shopModel != null) {
			shopDTO = new ShopDTO();
			shopDTO.setId(shopModel.getId());
			shopDTO.setName(shopModel.getName());
			shopDTO.setAddress(shopModel.getAddress());
			shopDTO.setOwnerId(shopModel.getOwnerId());
		}

		return shopDTO;
	}

	@Override
	public List<ShopDTO> findShopListByOwnerId(Long userId) {
		List<ShopModel> shopModelList = shopManager.findShopListByOwnerId(userId);
		List<ShopDTO> shopDTOList = null;
		if (!CollectionUtils.isEmpty(shopModelList)) {
			shopDTOList = new ArrayList<ShopDTO>();
			ShopDTO shopDTO = null;
			for (ShopModel shopModel : shopModelList) {
				shopDTO = new ShopDTO();
				shopDTO.setId(shopModel.getId());
				shopDTO.setName(shopModel.getName());
				shopDTO.setAddress(shopModel.getAddress());
				shopDTO.setOwnerId(shopModel.getOwnerId());
				shopDTOList.add(shopDTO);
			}
		}
		return shopDTOList;
	}

}
