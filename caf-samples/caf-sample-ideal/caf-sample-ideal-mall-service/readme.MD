(1).apollo配置中心，私有空间application需要配置：

app.logging.path = D:\logs\caf-sample-allinone-service
#
app.db.mall.data-source.url = ******************************************************************************************************
app.db.mall.data-source.username = root
app.db.mall.data-source.password = newPassword123@
app.db.mall.data-source.initial-size = 2
app.db.mall.data-source.max-active = 10
app.db.mall.data-source.max-idle = 16
app.db.mall.data-source.min-idle = 2
app.db.mall.type-aliases-package = com.coohua.allinone.rpc.model
#
app.db.user.data-source.url = ******************************************************************************************************
app.db.user.data-source.username = root
app.db.user.data-source.password = newPassword123@
app.db.user.data-source.initial-size = 2
app.db.user.data-source.max-active = 10
app.db.user.data-source.max-idle = 16
app.db.user.data-source.min-idle = 2
app.db.user.type-aliases-package = com.coohua.allinone.rpc.model
#
app.jedis-cluster.mall.address = ************:9700,************:9701,************:9702
app.jedis-cluster.mall.pool.max-total = 301
app.jedis-cluster.mall.pool.max-idle = 11
app.jedis-cluster.mall.pool.min-idle = 1
app.jedis-cluster.mall.pool.max-wait-millis = 6001

app.jedis-cluster.user.address = ************:9700,************:9702
app.jedis-cluster.user.pool.max-total = 301
app.jedis-cluster.user.pool.max-idle = 11
app.jedis-cluster.user.pool.min-idle = 1
app.jedis-cluster.user.pool.max-wait-millis = 6001
#
app.motan.user.port = 1234
app.motan.user.registry.address = ************:2181
app.motan.mall.port = 1235
app.motan.mall.registry.address = ************:2181



(2).样例运行程序需要增加启动参数:

-Denv=dev -Dapollo.cluster=default -Dapp.id=caf-sample-allinone-service -Dapollo.meta=http://apollo-dev-1:8080,http://apollo-dev-2:8081,http://apollo-dev-3:8082