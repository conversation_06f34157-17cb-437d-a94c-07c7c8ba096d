package com.coohua.app.sample.apollo.config;

import org.springframework.beans.factory.annotation.Value;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@ToString
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class TestConfigBean {

	@Value("${test.testUrl}")
	private String testUrl;

	@Value("${test.currentENV}")
	private String currentEnv;

}
