package com.coohua.app.sample.apollo;

import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import com.coohua.app.sample.apollo.config.TestConfigBean;
import com.coohua.caf.core.apollo.EnableAutoChangeApolloConfig;
import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;

import lombok.extern.slf4j.Slf4j;

/**
 * Created by zhangrongbin on 2018/09/27.
 */
@EnableApolloConfig(value = { "application", "caf.test" })
@EnableAutoChangeApolloConfig
@SpringBootApplication
@Slf4j
public class SimpleApolloApplication {
	public static void main(String[] args) {
		SpringApplication.run(SimpleApolloApplication.class, args);
	}

	@Component
	class Runner implements ApplicationRunner, ApplicationContextAware {

		private ApplicationContext applicationContext;

		@Autowired
		private TestConfigBean testConfigBean;

		@Override
		public void run(ApplicationArguments args) {
			// for (int i = 0; i < 1; i++) {
			for (;;) {
				log.info("testConfigBean:" + testConfigBean);
				try {
					Thread.sleep(3000);
				} catch (InterruptedException e) {
					e.printStackTrace();
				}
			}
		}

		@Override
		public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
			this.applicationContext = applicationContext;
		}
	}
}
