package com.coohua.app.sample.rocketmq.novel.scheduler;

import javax.annotation.Resource;

import org.apache.rocketmq.client.producer.SendResult;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.coohua.caf.core.rocketmq.producer.RocketMQBaseProducer;

@Component
public class NovelScheduler {

	private ApplicationContext applicationContext;

	@Resource(name = "novel-producer")
	private RocketMQBaseProducer novelProducer;

	private String TOPIC_NOVEL_ORDER = "novelOrder";

	private String PRODUCER_GROUP_NOVEL_ORDER = "novelOrderGroup";

	private static volatile boolean hasStarted = false;

	@Scheduled(fixedRate = 1000)
	public void sendMsg() throws Exception {

		startAdProducer();

		try {
			SendResult sendResult1 = novelProducer.send(TOPIC_NOVEL_ORDER,
					"novelOrderId:" + System.currentTimeMillis());
			System.out.println(sendResult1);

		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private void startAdProducer() throws Exception {
		if (!hasStarted) {
			synchronized (NovelScheduler.class) {
				if (!hasStarted) {
					novelProducer.setProducerGroup(PRODUCER_GROUP_NOVEL_ORDER);
					novelProducer.start();
					hasStarted = true;
				}
			}
		}
	}

}
