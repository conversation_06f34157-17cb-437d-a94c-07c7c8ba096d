package com.coohua.app.sample.rocketmq.singlePmultiC.scheduler;

import java.util.List;

import javax.annotation.Resource;

import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.coohua.caf.core.rocketmq.consumer.RocketMQBaseConsumer;
import com.coohua.caf.core.rocketmq.core.consumer.RocketMQConsumerProfiler;
import com.coohua.caf.core.rocketmq.core.consumer.RocketMQConsumerProfiler.Processor;
import com.coohua.caf.core.rocketmq.producer.RocketMQBaseProducer;

@Component
public class AdScheduler {

	private ApplicationContext applicationContext;

	@Resource(name = "topic1-producer")
	private RocketMQBaseProducer rocketmqProducer;

	// consumer1
	@Resource(name = "topic1-consumer1")
	private RocketMQBaseConsumer topic1Consumer1;

	// consumer2
	@Resource(name = "topic1-consumer2")
	private RocketMQBaseConsumer topic1Consumer2;

	// consumer3
	@Resource(name = "topic1-consumer3")
	private RocketMQBaseConsumer topic1Consumer3;

	private String TOPIC1 = "topic1";

	private final String PRODUCER_GROUP = "adGroup";

	private final String CONSUMER_GROUP = "adGroup";

	private static volatile boolean producerHasStarted = false;

	private static volatile boolean consumerHasStarted = false;

	@Scheduled(fixedRate = 1000)
	public void produceMsg() throws Exception {

		startProducer();
		try {
			int random1 = (int) (Math.random() * 100);
			int random2 = (int) (Math.random() * 100);
			int random3 = (int) (Math.random() * 100);
			sendMsg(TOPIC1, random2, "Id:" + System.currentTimeMillis());
			sendMsg(TOPIC1, random1, "Id:" + System.currentTimeMillis());
			sendMsg(TOPIC1, random3, "Id:" + System.currentTimeMillis());
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	@Scheduled(fixedRate = 1000)
	public void consumeMsg() throws Exception {
		startProducer();
		startConsumers();
	}

	private void sendMsg(String topic, int count, String msgContent) {
		new Thread(new Runnable() {

			@Override
			public void run() {
				for (int i = 0; i < count; i++) {
					long t1_nano = System.nanoTime();
					long t1_ms = System.currentTimeMillis();
					SendResult result = rocketmqProducer.send(topic, ((int) (Math.random() * 100))+"-"+msgContent);
					System.out.println(result);
					long t2_nano = System.nanoTime();
					long t2_ms = System.currentTimeMillis();

					long nanoTime = t2_nano - t1_nano;
					long msTime = t2_ms - t1_ms;
					System.out.println("nanoTime:" + nanoTime + ", msTime:" + msTime);
				}
			}
		}).start();

	}

	private void startProducer() throws Exception {
		if (!producerHasStarted) {
			synchronized (AdScheduler.class) {
				if (!producerHasStarted) {
					rocketmqProducer.setProducerGroup(PRODUCER_GROUP);
					rocketmqProducer.start();

					producerHasStarted = true;
				}
			}
		}
	}

	private void startConsumers() throws Exception {
		if (!consumerHasStarted) {
			synchronized (AdScheduler.class) {
				if (!consumerHasStarted) {
					startTopic1Consumer1();
					startTopic1Consumer2();
					startTopic1Consumer3();
					consumerHasStarted = true;
				}
			}
		}
	}

	private void startTopic1Consumer1() throws Exception {
		String rtbsConsumerGroup = "1"+CONSUMER_GROUP;
		topic1Consumer1.subscribe(TOPIC1);
		topic1Consumer1.setConsumerGroup(rtbsConsumerGroup);
		topic1Consumer1.registerMessageListener(new MessageListenerConcurrently() {

			@Override
			public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext context) {
				for (MessageExt ext : msgs) {
					Processor processor = RocketMQConsumerProfiler.beginProcessor(TOPIC1, rtbsConsumerGroup,
							AdScheduler.class, ext.getBornTimestamp(), ext.getStoreTimestamp());
					try {
						System.out.printf("topic1Consumer1 topic:%s bt:%s st:%s %n", TOPIC1, ext.getBornTimestamp(),
								ext.getStoreTimestamp());
					} catch (Exception e) {
						processor.exception();
						e.printStackTrace();
					} finally {
						processor.complete();
					}
				}
				return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
			}
		});
		topic1Consumer1.start();
	}

	private void startTopic1Consumer2() throws Exception {
		String consumerGroup = "2"+CONSUMER_GROUP;
		topic1Consumer2.subscribe(TOPIC1);
		topic1Consumer2.setConsumerGroup(consumerGroup);
		topic1Consumer2.registerMessageListener(new MessageListenerConcurrently() {

			@Override
			public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext context) {
				for (MessageExt ext : msgs) {
					Processor processor = RocketMQConsumerProfiler.beginProcessor(TOPIC1, consumerGroup,
							AdScheduler.class, ext.getBornTimestamp(), ext.getStoreTimestamp());
					try {
						System.out.printf("topic1Consumer2 topic:%s %s Receive New Message: %s %n", TOPIC1,
								Thread.currentThread().getName(), new String(ext.getBody(), "UTF-8"));
					} catch (Exception e) {
						processor.exception();
						e.printStackTrace();
					} finally {
						processor.complete();
					}
				}
				return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
			}
		});
		topic1Consumer2.start();
	}

	private void startTopic1Consumer3() throws Exception {
		String sspConsumerGroup = "3"+CONSUMER_GROUP;
		topic1Consumer3.subscribe(TOPIC1);
		topic1Consumer3.setConsumerGroup(sspConsumerGroup);
		topic1Consumer3.registerMessageListener(new MessageListenerConcurrently() {

			@Override
			public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext context) {
				for (MessageExt ext : msgs) {
					Processor processor = RocketMQConsumerProfiler.beginProcessor(TOPIC1, sspConsumerGroup,
							AdScheduler.class, ext.getBornTimestamp(), ext.getStoreTimestamp());
					try {
						System.out.printf("topic1Consumer3 topic:%s %s Receive New Message: %s %n", TOPIC1,
								Thread.currentThread().getName(), new String(ext.getBody(), "UTF-8"));
					} catch (Exception e) {
						processor.exception();
						e.printStackTrace();
					} finally {
						processor.complete();
					}
				}
				return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
			}
		});
		topic1Consumer3.start();
	}

}
