package com.coohua.app.sample.rocketmq.singlePmultiC;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableScheduling;

import com.coohua.caf.core.apollo.EnableAutoChangeApolloConfig;
import com.coohua.caf.core.rocketmq.core.consumer.EnableRocketMQConsumer;
import com.coohua.caf.core.rocketmq.core.producer.EnableRocketMQProducer;
import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;

import lombok.extern.slf4j.Slf4j;

/**
 * 
 * -Denv=dev -Dapp.id=caf.sample.rocketmq -Dapollo.cluster=ad
 * 
 * 对同一个topic启动一个producer,3个consumer.
 * 
 * <AUTHOR>
 * @version create time: 2018年10月25日 上午11:04:18
 *
 */
@SpringBootApplication

//开启rocketmq
@EnableRocketMQProducer(namespace = "topic1-producer")
@EnableRocketMQConsumer(namespace = "topic1-consumer1")
@EnableRocketMQConsumer(namespace = "topic1-consumer2")
@EnableRocketMQConsumer(namespace = "topic1-consumer3")

//开启apollo配置中心
@EnableApolloConfig(value = { "caf.mq.rocketmq.namesrv", "caf.mq.rocketmq.producer", "caf.mq.rocketmq.consumer" })
//系统启动时会打印@EnableApolloConfig中指定的namespace的初始化值；并且如果运行时会打印发生变化的配置。
@EnableAutoChangeApolloConfig
@EnableScheduling
@Slf4j
public class CafSampleAdApplication {

	public static void main(String[] args) {
		// System.getProperties().setProperty("server.port", "8072");
		SpringApplication.run(CafSampleAdApplication.class, args);
	}

}
