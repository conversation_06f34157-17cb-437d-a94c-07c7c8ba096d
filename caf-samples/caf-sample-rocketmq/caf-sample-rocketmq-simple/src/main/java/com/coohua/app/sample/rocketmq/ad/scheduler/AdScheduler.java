package com.coohua.app.sample.rocketmq.ad.scheduler;

import java.util.List;

import javax.annotation.Resource;

import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.coohua.caf.core.rocketmq.consumer.RocketMQBaseConsumer;
import com.coohua.caf.core.rocketmq.core.consumer.RocketMQConsumerProfiler;
import com.coohua.caf.core.rocketmq.core.consumer.RocketMQConsumerProfiler.Processor;
import com.coohua.caf.core.rocketmq.producer.RocketMQBaseProducer;

@Component
public class AdScheduler {

	private ApplicationContext applicationContext;

	@Resource(name = "rocketmq-producer")
	private RocketMQBaseProducer rocketmqProducer;

	// RTBS：实时竞价平台提供广告的一方。
	@Resource(name = "rtbs-consumer")
	private RocketMQBaseConsumer rtbsConsumer;

	// RTBD：实时竞价平台需求广告的一方。
	@Resource(name = "rtbd-consumer")
	private RocketMQBaseConsumer rtbdConsumer;

	// SSP（Supply-Side Platform）：供应方平台，面向服务。
	@Resource(name = "ssp-consumer")
	private RocketMQBaseConsumer sspConsumer;

	private String TOPIC_RTBS_ORDER = "rtbsOrder";

	private String TOPIC_RTBD_ORDER = "rtbdOrder";

	private String TOPIC_SSP_ORDER = "sspOrder";

	private final String PRODUCER_GROUP = "adGroup";

	private final String CONSUMER_GROUP = "adGroup";

	private static volatile boolean producerHasStarted = false;

	private static volatile boolean consumerHasStarted = false;

	@Scheduled(fixedRate = 1000)
	public void produceMsg() throws Exception {

		startProducer();
		try {
			int random1 = (int) (Math.random() * 500);
			int random2 = (int) (Math.random() * 500);
			int random3 = (int) (Math.random() * 500);
			sendMsg(TOPIC_RTBS_ORDER, random2, "rtbsOrderId:" + System.currentTimeMillis());
			sendMsg(TOPIC_RTBD_ORDER, random1, "rtbdOrderId:" + System.currentTimeMillis());
			sendMsg(TOPIC_SSP_ORDER, random3, "sspOrderId:" + System.currentTimeMillis());
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	@Scheduled(fixedRate = 1000)
	public void consumeMsg() throws Exception {
		startProducer();
		startConsumers();
	}

	private void sendMsg(String topic, int count, String msgContent) {
		new Thread(new Runnable() {

			@Override
			public void run() {
				for (int i = 0; i < count; i++) {
					long t1_nano = System.nanoTime();
					long t1_ms = System.currentTimeMillis();
					SendResult result = rocketmqProducer.send(topic, msgContent);
					System.out.println(result);
					long t2_nano = System.nanoTime();
					long t2_ms = System.currentTimeMillis();

					long nanoTime = t2_nano - t1_nano;
					long msTime = t2_ms - t1_ms;
					System.out.println("nanoTime:" + nanoTime + ", msTime:" + msTime);
				}
			}
		}).start();

	}

	private void startProducer() throws Exception {
		if (!producerHasStarted) {
			synchronized (AdScheduler.class) {
				if (!producerHasStarted) {
					rocketmqProducer.setProducerGroup(PRODUCER_GROUP);
					rocketmqProducer.start();

					producerHasStarted = true;
				}
			}
		}
	}

	private void startConsumers() throws Exception {
		if (!consumerHasStarted) {
			synchronized (AdScheduler.class) {
				if (!consumerHasStarted) {
					startRTBSConsumer();
					startRTBDConsumer();
					startSSPConsumer();
					consumerHasStarted = true;
				}
			}
		}
	}

	private void startRTBSConsumer() throws Exception {
		String rtbsConsumerGroup = "rtbs-" + CONSUMER_GROUP;
		rtbsConsumer.subscribe(TOPIC_RTBS_ORDER);
		rtbsConsumer.setConsumerGroup(rtbsConsumerGroup);
		rtbsConsumer.registerMessageListener(new MessageListenerConcurrently() {

			@Override
			public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext context) {
				for (MessageExt ext : msgs) {
					Processor processor = RocketMQConsumerProfiler.beginProcessor(TOPIC_RTBS_ORDER, rtbsConsumerGroup,
							AdScheduler.class, ext.getBornTimestamp(), ext.getStoreTimestamp());
					try {
						System.out.printf("bt:%s st:%s %n", ext.getBornTimestamp(), ext.getStoreTimestamp());

					} catch (Exception e) {
						processor.exception();
						e.printStackTrace();
					} finally {
						processor.complete();
					}
				}
				return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
			}
		});
		rtbsConsumer.start();
	}

	private void startRTBDConsumer() throws Exception {
		String consumerGroup = "rtbd-" + CONSUMER_GROUP;
		rtbdConsumer.subscribe(TOPIC_RTBD_ORDER);
		rtbdConsumer.setConsumerGroup(consumerGroup);
		rtbdConsumer.registerMessageListener(new MessageListenerConcurrently() {

			@Override
			public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext context) {
				for (MessageExt ext : msgs) {
					Processor processor = RocketMQConsumerProfiler.beginProcessor(TOPIC_RTBD_ORDER, consumerGroup,
							AdScheduler.class, ext.getBornTimestamp(), ext.getStoreTimestamp());
					try {
						System.out.printf("%s Receive New Message: %s %n", Thread.currentThread().getName(),
								new String(ext.getBody(), "UTF-8"));
					} catch (Exception e) {
						processor.exception();
						e.printStackTrace();
					} finally {
						processor.complete();
					}
				}
				return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
			}
		});
		rtbdConsumer.start();
	}

	private void startSSPConsumer() throws Exception {
		String sspConsumerGroup = "ssp-" + CONSUMER_GROUP;
		sspConsumer.subscribe(TOPIC_SSP_ORDER);
		sspConsumer.setConsumerGroup(sspConsumerGroup);
		sspConsumer.registerMessageListener(new MessageListenerConcurrently() {

			@Override
			public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext context) {
				for (MessageExt ext : msgs) {
					Processor processor = RocketMQConsumerProfiler.beginProcessor(TOPIC_SSP_ORDER, sspConsumerGroup,
							AdScheduler.class, ext.getBornTimestamp(), ext.getStoreTimestamp());
					try {
						System.out.printf("%s Receive New Message: %s %n", Thread.currentThread().getName(),
								new String(ext.getBody(), "UTF-8"));
					} catch (Exception e) {
						processor.exception();
						e.printStackTrace();
					} finally {
						processor.complete();
					}
				}
				return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
			}
		});
		sspConsumer.start();
	}

}
