package com.coohua.app.sample.rocketmq.scenes2.producer;

import java.util.concurrent.TimeUnit;

import javax.annotation.Resource;

import org.apache.rocketmq.client.producer.SendResult;
import org.springframework.beans.BeansException;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import com.coohua.app.sample.rocketmq.scenes2.BaseApplication;
import com.coohua.caf.core.apollo.EnableAutoChangeApolloConfig;
import com.coohua.caf.core.rocketmq.core.producer.EnableRocketMQProducer;
import com.coohua.caf.core.rocketmq.producer.RocketMQBaseProducer;
import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;

import lombok.extern.slf4j.Slf4j;

/**
 * 
 * <AUTHOR>
 * @version create time: 2018年10月25日 上午11:04:18
 *
 */
@SpringBootApplication

//开启rocketmq
//表示广告业务--生产者--广告主服务
@EnableRocketMQProducer(namespace = "ad-producer-advertisers")

//开启apollo配置中心
@EnableApolloConfig(value = { "caf.mq.rocketmq.namesrv", "application" })
//系统启动时会打印@EnableApolloConfig中指定的namespace的初始化值；并且如果运行时会打印发生变化的配置。
@EnableAutoChangeApolloConfig

@Slf4j
public class CafSampleRocketMQApplicationProducer extends BaseApplication {

	public static void main(String[] args) {
		SpringApplication.run(CafSampleRocketMQApplicationProducer.class, args);
	}

	@Component
	class Runner implements ApplicationRunner, ApplicationContextAware {

		private ApplicationContext applicationContext;

		// 表示广告业务--生产者--广告主服务
		@Resource(name = "ad-producer-advertisers")
		private RocketMQBaseProducer adProducer1;

		private static final String TOPIC_GROUP_ADVERTISERS = "topic-producerGroup-advertisers";

		@Override
		public void run(ApplicationArguments args) throws Exception {

			startProducer1();

			while (true) {
				try {
					SendResult sendResult1 = adProducer1.send(TOPIC_SCENES2,
							TOPIC_SCENES2 + ":producer1-haha:" + System.currentTimeMillis());
					System.out.println(sendResult1);

					TimeUnit.SECONDS.sleep(1);
					// break;
				} catch (Exception e) {
					log.error(e.getMessage(), e);
					try {
						TimeUnit.SECONDS.sleep(3);
					} catch (InterruptedException e1) {
						// TODO Auto-generated catch block
						e1.printStackTrace();
					}
				}
			}
		}

		private void startProducer1() throws Exception {
			adProducer1.setProducerGroup(TOPIC_GROUP_ADVERTISERS);
			adProducer1.start();
		}

		@Override
		public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
			this.applicationContext = applicationContext;
		}
	}
}
