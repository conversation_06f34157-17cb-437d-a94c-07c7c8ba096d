package com.coohua.app.sample.rocketmq.scenes2.ad.spiderConsumerGroup.consumer2;

import java.io.UnsupportedEncodingException;
import java.util.List;
import java.util.concurrent.TimeUnit;

import javax.annotation.Resource;

import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.BeansException;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import com.coohua.app.sample.rocketmq.scenes2.ad.spiderConsumerGroup.BaseAdSpiderConsumerApplication;
import com.coohua.caf.core.apollo.EnableAutoChangeApolloConfig;
import com.coohua.caf.core.rocketmq.consumer.RocketMQBaseConsumer;
import com.coohua.caf.core.rocketmq.core.consumer.EnableRocketMQConsumer;
import com.coohua.caf.core.rocketmq.core.producer.EnableRocketMQProducer;
import com.coohua.caf.core.rocketmq.producer.RocketMQBaseProducer;
import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;

import lombok.extern.slf4j.Slf4j;

/**
 * 
 * <AUTHOR>
 * @version create time: 2018年10月25日 上午11:04:18
 *
 */
@SpringBootApplication

//开启rocketmq
//表示广告业务--生产者--广告主服务
@EnableRocketMQConsumer(namespace = "ad-consumer-spider")

//开启apollo配置中心
@EnableApolloConfig(value = { "caf.mq.rocketmq.namesrv", "caf.mq.rocketmq.consumer" })
//系统启动时会打印@EnableApolloConfig中指定的namespace的初始化值；并且如果运行时会打印发生变化的配置。
@EnableAutoChangeApolloConfig

@Slf4j
public class CafSampleRocketMQApplicationAdSpiderConsume2 extends BaseAdSpiderConsumerApplication {

	public static void main(String[] args) {
		SpringApplication.run(CafSampleRocketMQApplicationAdSpiderConsume2.class, args);
	}

	@Component
	class Runner implements ApplicationRunner, ApplicationContextAware {

		private ApplicationContext applicationContext;

		// 表示广告业务--生产者--广告主服务
		@Resource(name = "ad-consumer-spider")
		private RocketMQBaseConsumer adConsumer;

		@Override
		public void run(ApplicationArguments args) throws Exception {
			this.startConsumer();
		}

		private void startConsumer() throws Exception {
			adConsumer.subscribe(TOPIC_SCENES2);
			adConsumer.setConsumerGroup(TOPIC_GROUP_SPIDER);
			adConsumer.registerMessageListener(new MessageListenerConcurrently() {

				@Override
				public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs,
						ConsumeConcurrentlyContext context) {
					// TODO biz code

					System.out.printf("Consuemr1 %s Receive New Messages: %s %n", Thread.currentThread().getName(),
							msgs);
					for (MessageExt ext : msgs) {
						try {
							System.out.printf("%s Receive New Message: %s %n", Thread.currentThread().getName(),
									new String(ext.getBody(), "UTF-8"));
						} catch (UnsupportedEncodingException e) {
							// TODO Auto-generated catch block
							e.printStackTrace();
						}
					}
					return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
				}
			});
			adConsumer.start();
		}

		@Override
		public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
			this.applicationContext = applicationContext;
		}
	}
}
