(1).apollo配置中心，私有空间application需要配置：
app.logging.path = D:\logs\cafSingleDBApolloServer
app.db.default.data-source.url = ******************************************************************************************************
app.db.default.data-source.username = root
app.db.default.data-source.password = newPassword123@

(2).样例运行程序需要增加启动参数:

-Denv=dev -Dapollo.cluster=default -Dapp.id=cafSingleDBApolloServer -Dapollo.meta=http://apollo-dev-1:8080,http://apollo-dev-2:8081,http://apollo-dev-3:8082