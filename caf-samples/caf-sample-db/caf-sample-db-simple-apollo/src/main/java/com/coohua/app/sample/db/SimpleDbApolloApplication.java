package com.coohua.app.sample.db;

import java.util.concurrent.TimeUnit;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.stereotype.Component;

import com.coohua.app.sample.db.mapper.ShopMapper;
import com.coohua.app.sample.db.model.Shop;
import com.coohua.caf.core.db.EnableDataSource;
import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;

/**
 * Created by zhangrongbin on 2018/09/27.
 */
@SpringBootApplication
@EnableDataSource(mapperPackages = "com.coohua.app.sample.db.mapper")
@EnableApolloConfig
public class SimpleDbApolloApplication {

	private static final Logger logger = LoggerFactory.getLogger(SimpleDbApolloApplication.class);

	public static void main(String[] args) {
		SpringApplication.run(SimpleDbApolloApplication.class, args);
	}

	@Component
	class Runner implements ApplicationRunner {
		@Autowired
		private ShopMapper shopMapper;

		@Override
		public void run(ApplicationArguments args) {
			while (true) {
				try {
					Shop shop = shopMapper.findById(1);
					logger.info(ToStringBuilder.reflectionToString(shop, ToStringStyle.MULTI_LINE_STYLE));

					TimeUnit.SECONDS.sleep(1);
				} catch (Exception e) {
					logger.error(e.getMessage(), e);
				}
			}
		}
	}
}
