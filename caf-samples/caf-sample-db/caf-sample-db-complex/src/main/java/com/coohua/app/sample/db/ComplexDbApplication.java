package com.coohua.app.sample.db;

import com.coohua.app.sample.db.mapper.malldb.ShopMapper;
import com.coohua.app.sample.db.mapper.userdb.UserMapper;
import com.coohua.app.sample.db.model.malldb.Shop;
import com.coohua.app.sample.db.model.userdb.User;
import com.coohua.caf.core.db.EnableDataSource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.stereotype.Component;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * Created by zhangrongbin on 2018/09/27.
 */
@SpringBootApplication
@EnableDataSource(namespace = "user", mapperPackages = "com.coohua.app.sample.db.mapper.userdb")
@EnableDataSource(namespace = "mall", mapperPackages = "com.coohua.app.sample.db.mapper.malldb")
@Slf4j
public class ComplexDbApplication {
    public static final ExecutorService executor = Executors.newFixedThreadPool(10);
    public static void main(String[] args) {
        SpringApplication.run(ComplexDbApplication.class, args);
    }

    @Component
    class Runner implements ApplicationRunner {
        @Autowired
        private ShopMapper shopMapper;
        @Autowired
        private UserMapper userMapper;
        @Override
        public void run(ApplicationArguments args) {
            executor.submit(() -> {
                for (;;) {
                //for (int i = 0; i < 10; i++) {
                    Shop shop = shopMapper.findById(1);
                    log.info(shop.toString());
                    User user = userMapper.findById(1);
                    log.info(user.toString());
                }
            });
        }
    }
}
