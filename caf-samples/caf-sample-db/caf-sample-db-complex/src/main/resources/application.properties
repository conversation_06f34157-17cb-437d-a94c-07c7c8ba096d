app.logging.path=logs/caf-sample-db-complex
app.db.mall.data-source.stat.enabled=true
app.db.mall.data-source.stat.logSlowSql=true
app.db.mall.data-source.stat.slowSqlMillis=50
app.db.mall.data-source.url=******************************************************************************************************
app.db.mall.data-source.username=root
app.db.mall.data-source.password=newPassword123@
app.db.mall.data-source.initial-size=2
app.db.mall.data-source.max-active=10
app.db.mall.data-source.min-idle=2
app.db.mall.data-source.max-wait=6000
app.db.mall.data-source.time-between-eviction-runs-millis=60000
app.db.mall.data-source.min-evictable-idle-time-millis=300000
app.db.mall.data-source.time-between-log-stats-millis=300000
app.db.mall.data-source.validation-query=SELECT 'x'
app.db.mall.data-source.test-while-idle=true
app.db.mall.data-source.test-on-borrow=false
app.db.mall.data-source.test-on-return=false
app.db.mall.type-aliases-package=com.coohua.app.sample.db.model.malldb
app.db.user.data-source.url=******************************************************************************************************
app.db.user.data-source.username=root
app.db.user.data-source.password=newPassword123@
app.db.user.data-source.initial-size=2
app.db.user.data-source.max-active=10
app.db.user.data-source.min-idle=2
app.db.user.type-aliases-package=com.coohua.app.sample.db.model.userdb

spring.profiles.active=dev