package com.coohua.app.sample.db;

import com.coohua.app.sample.db.mapper.malldb.ShopMapper;
import com.coohua.app.sample.db.mapper.userdb.UserMapper;
import com.coohua.app.sample.db.model.malldb.Shop;
import com.coohua.app.sample.db.model.userdb.User;
import com.coohua.caf.core.db.EnableDataSource;
import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;

import java.util.concurrent.TimeUnit;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.mybatis.spring.annotation.MapperScan;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.stereotype.Component;

/**
 * Created by z<PERSON><PERSON><PERSON> on 2018/09/27.
 */
@SpringBootApplication
@EnableDataSource(namespace = "user", mapperPackages = "com.coohua.app.sample.db.mapper.userdb")
@EnableDataSource(namespace = "mall", mapperPackages = "com.coohua.app.sample.db.mapper.malldb")
@EnableApolloConfig
public class ComplexDbApolloApplication {
	private static final Logger logger = LoggerFactory.getLogger(ComplexDbApolloApplication.class);

	public static void main(String[] args) {
		SpringApplication.run(ComplexDbApolloApplication.class, args);
	}

	@Component
	class Runner implements ApplicationRunner {
		@Autowired
		private ShopMapper shopMapper;
		@Autowired
		private UserMapper userMapper;

		@Override
		public void run(ApplicationArguments args) {
			while (true) {
				try {
					Shop shop = shopMapper.findById(1);
					logger.info(ToStringBuilder.reflectionToString(shop, ToStringStyle.MULTI_LINE_STYLE));
					User user = userMapper.findById(1);
					logger.info(ToStringBuilder.reflectionToString(user, ToStringStyle.MULTI_LINE_STYLE));

					TimeUnit.SECONDS.sleep(1);
				} catch (Exception e) {
					logger.error(e.getMessage(), e);
				}
			}
		}
	}
}
