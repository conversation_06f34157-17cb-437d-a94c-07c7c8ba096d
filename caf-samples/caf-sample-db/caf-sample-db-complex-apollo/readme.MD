(1).apollo配置中心，私有空间application需要配置：
app.logging.path = D:\logs\cafComplexDBApolloServer
app.db.mall.data-source.url = ******************************************************************************************************
app.db.mall.data-source.username = root
app.db.mall.data-source.password = newPassword123@
app.db.user.data-source.url = ******************************************************************************************************
app.db.user.data-source.username = root
app.db.user.data-source.password = newPassword123@

(2).样例运行程序需要增加启动参数:

-Denv=dev -Dapollo.cluster=default -Dapp.id=cafComplexDBApolloServer -Dapollo.meta=http://apollo-dev-1:8080,http://apollo-dev-2:8081,http://apollo-dev-3:8082