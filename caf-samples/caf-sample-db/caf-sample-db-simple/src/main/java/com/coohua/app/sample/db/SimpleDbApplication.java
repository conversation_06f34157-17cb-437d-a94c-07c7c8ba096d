package com.coohua.app.sample.db;

import com.coohua.app.sample.db.mapper.ShopMapper;
import com.coohua.app.sample.db.model.Shop;
import com.coohua.caf.core.db.EnableDataSource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.mybatis.spring.annotation.MapperScan;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.stereotype.Component;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * Created by z<PERSON><PERSON><PERSON> on 2018/09/27.
 */
@SpringBootApplication
@EnableDataSource(mapperPackages = "com.coohua.app.sample.db.mapper")
@Slf4j
public class SimpleDbApplication {
    public static void main(String[] args) {
        SpringApplication.run(SimpleDbApplication.class, args);
    }

    @Component
    static class Runner implements ApplicationRunner {
        private static final ExecutorService EXECUTOR = Executors.newSingleThreadExecutor();
        @Autowired
        private ShopMapper shopMapper;
        @Override
        public void run(ApplicationArguments args) {
            EXECUTOR.submit(() -> {
                for (;;) {
                    //for (;;) {
                    Shop shop = shopMapper.findById(1);
                    log.info(shop.toString());
                    try {
                        TimeUnit.MILLISECONDS.sleep(30);
                    } catch (InterruptedException e) {
                    }
                }
            });
        }
    }
}
